/* styles/dimensions.css - Dimension-specific styles with critical layout fixes */

/* CRITICAL FIX: Common Game Dimension Styles with proper scroll handling */
.game-dimension {
    flex-direction: column;
    padding: 0 var(--spacing-lg, 2rem);
    
    /* CRITICAL FIX: Proper height and scroll management */
    min-height: 100vh;
    min-height: 100dvh;
    
    /* CRITICAL FIX: Enable vertical scrolling within game dimensions */
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    overscroll-behavior-x: none;
    
    /* PERFORMANCE FIX: Better scrolling performance */
    scroll-behavior: smooth;
    will-change: scroll-position;
	align-items: flex-start;
	justify-content: flex-start; /* ← Start content from top-left */
}

/* CRITICAL FIX: Add top margin to content for navigation clearance */
.game-dimension .dimension-content {
    padding-top: max(100px, env(safe-area-inset-top, 0) + 100px);
    padding-bottom: max(100px, env(safe-area-inset-bottom, 0) + 100px);
    margin-top: 0;
    margin-bottom: 0;
}

/* CRITICAL FIX: Prevent content from extending beyond viewport */
.game-dimension > * {
    flex-shrink: 0;
    max-width: 100%;
    box-sizing: border-box;
}

/* CRITICAL FIX: Content wrapper for better layout control */
.dimension-content {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm, 1rem);
    box-sizing: border-box;
    
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Dimension Headers - Consistent design with game-specific accents */
.dimension-header {
    text-align: center;
    margin-bottom: var(--spacing-xl, 3rem);
    position: relative;
    z-index: 10;
    width: 100%;
    flex-shrink: 0;
}

.dimension-title {
    font-size: var(--font-size-3xl, 4rem);
    font-size: clamp(2rem, 6vw, 4rem);
    font-weight: bold;
    margin-bottom: var(--spacing-sm, 1rem);
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    display: inline-block;
    padding: 0 var(--spacing-lg, 2rem);
    
    /* CRITICAL FIX: Better text wrapping */
    word-wrap: break-word;
    hyphens: auto;
    max-width: 100%;
}

/* CRITICAL FIX: Base title styling with better shadow for readability */
.dimension-title::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -20px;
    right: -20px;
    bottom: -10px;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    z-index: -1;
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* COMPATIBILITY FIX: Backdrop-filter fallback */
@supports not (backdrop-filter: blur(10px)) {
    .dimension-title::before {
        background: rgba(0, 0, 0, 0.95);
    }
}

.dimension-subtitle {
    font-size: var(--font-size-lg, 1.5rem);
    font-size: clamp(1rem, 3vw, 1.5rem);
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    font-weight: 300;
    margin-top: var(--spacing-xs, 0.5rem);
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    max-width: 100%;
}

/* CRITICAL FIX: Home Dimension with better layout and proper centering - Updated for Orange/Red Theme */
.home-dimension {
    background: radial-gradient(ellipse at center, rgba(255, 102, 0, 0.1) 0%, transparent 70%);
    flex-direction: column;
    text-align: center;
    justify-content: center;
    align-items: center;
    
    /* CRITICAL FIX: Proper padding and height for desktop */
    padding: var(--spacing-xl, 3rem) var(--spacing-lg, 2rem);
    min-height: 100vh;
    min-height: 100dvh;
    
    /* CRITICAL FIX: Ensure content is properly contained */
    box-sizing: border-box;
}

/* CRITICAL FIX: Logo matrix with better positioning */
.logo-matrix {
    font-size: var(--font-size-4xl, 6rem);
    font-size: clamp(2.5rem, 10vw, 6rem);
    font-weight: bold;
    
    /* CRITICAL FIX: Better gradient handling */
    background: linear-gradient(45deg, var(--primary-glow, #00ffff), var(--secondary-glow, #ff00ff), var(--tertiary-glow, #ffff00));
    background-size: 400% 400%;
    
    /* COMPATIBILITY FIX: Text gradient fallbacks */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    
    /* PERFORMANCE FIX: Better animation */
    animation: matrixFlow 3s ease-in-out infinite;
    will-change: background-position;
    
    text-shadow: 0 0 50px rgba(0, 255, 255, 0.5);
    margin-bottom: var(--spacing-lg, 2rem);
    
    /* CRITICAL FIX: Better text handling and ensure visibility */
    word-wrap: break-word;
    max-width: 100%;
    position: relative;
    z-index: 2;
}

/* COMPATIBILITY FIX: Text gradient fallback */
@supports not (-webkit-background-clip: text) {
    .logo-matrix {
        color: var(--primary-glow, #00ffff);
        background: none;
    }
}

@keyframes matrixFlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.tagline {
    font-size: var(--font-size-xl, 2rem);
    font-size: clamp(1.25rem, 4vw, 2rem);
    margin-bottom: var(--spacing-xl, 3rem);
    opacity: 0.8;
    
    /* PERFORMANCE FIX: Better animation */
    animation: float 2s ease-in-out infinite;
    will-change: transform;
    
    /* CRITICAL FIX: Better text handling and ensure visibility */
    word-wrap: break-word;
    max-width: 100%;
    padding: 0 var(--spacing-sm, 1rem);
    position: relative;
    z-index: 2;
}

@keyframes float {
    0%, 100% { 
        transform: translateY(0px);
        -webkit-transform: translateY(0px);
    }
    50% { 
        transform: translateY(-10px);
        -webkit-transform: translateY(-10px);
    }
}

/* CRITICAL FIX: Homepage game grid - horizontal on desktop, vertical on mobile */
.server-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr); /* 4 games in a row on desktop */
    gap: var(--spacing-lg, 2rem);
    margin-top: var(--spacing-lg, 2rem);
    width: 100%;
    max-width: 1200px; /* Increased for horizontal layout */
    
    /* Center the grid items */
    justify-items: center;
    align-items: center;
    
    /* CRITICAL FIX: Ensure proper z-index for visibility */
    position: relative;
    z-index: 2;
}

/* COMPATIBILITY FIX: Grid fallback for older browsers */
@supports not (display: grid) {
    .server-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .server-grid .game-portal {
        margin: var(--spacing-sm, 1rem);
        width: 200px;
        flex: 0 0 auto;
    }
}

/* CRITICAL FIX: Game logos - consistent positioning for all game pages */
.game-logo {
    width: 200px;
    height: 200px;
    object-fit: contain;
    margin: 0 auto var(--spacing-lg, 2rem) auto;
    display: block;
    opacity: 0.9;
    transition: opacity var(--transition-fast, 0.3s ease);
    
    /* Add subtle glow effect */
    filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.3));
    
    /* Ensure logos are visible */
    position: relative;
    z-index: 5;
}

.game-logo:hover {
    opacity: 1;
    filter: drop-shadow(0 0 30px rgba(0, 255, 255, 0.5));
}

/* CRITICAL FIX: Game icon images for home page portals */
.game-icon-img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    margin-bottom: var(--spacing-sm, 1rem);
    opacity: 0.9;
    transition: all var(--transition-fast, 0.3s ease);
    
    /* Add subtle glow effect */
    filter: drop-shadow(0 0 15px rgba(0, 255, 255, 0.3));
}

.game-portal:hover .game-icon-img {
    opacity: 1;
    transform: scale(1.1);
    filter: drop-shadow(0 0 25px rgba(0, 255, 255, 0.5));
}

/* Mobile adjustments for game icons */
@media (max-width: 768px) {
    .game-icon-img {
        width: 60px;
        height: 60px;
        margin-bottom: 0;
        margin-right: var(--spacing-md, 1.5rem);
    }
}

/* Game-specific logo styling */
.palworld-logo {
    filter: drop-shadow(0 0 20px rgba(255, 107, 157, 0.4));
}

.palworld-logo:hover {
    filter: drop-shadow(0 0 30px rgba(255, 107, 157, 0.6));
}

.sevendays-logo {
    filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.4));
}

.sevendays-logo:hover {
    filter: drop-shadow(0 0 30px rgba(255, 107, 53, 0.6));
}

.zomboid-logo {
    filter: drop-shadow(0 0 20px rgba(255, 107, 107, 0.4));
}

.zomboid-logo:hover {
    filter: drop-shadow(0 0 30px rgba(255, 107, 107, 0.6));
}

.dune-logo {
    filter: drop-shadow(0 0 20px rgba(255, 181, 53, 0.4));
}

.dune-logo:hover {
    filter: drop-shadow(0 0 30px rgba(255, 181, 53, 0.6));
}

/* Mobile adjustments for logos */
@media (max-width: 768px) {
    .game-logo {
        width: 150px;
        height: 150px;
        margin-bottom: var(--spacing-md, 1.5rem);
    }
}
.palworld-dimension {
    background: radial-gradient(ellipse at center, rgba(255, 107, 157, 0.15) 0%, transparent 70%),
                linear-gradient(180deg, #0a0a0a 0%, #1a0a2e 100%);
}

/* CRITICAL FIX: Palworld specific scrollable content */
.palworld-dimension .dimension-content {
    /* CRITICAL FIX: Ensure content can scroll properly */
    overflow: visible;
    padding-bottom: var(--spacing-xl, 3rem);
}

.palworld-dimension .dimension-title {
    background: linear-gradient(45deg, var(--palworld-accent, #ff6b9d), #faa2c1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* COMPATIBILITY FIX: Text gradient fallback */
@supports not (-webkit-background-clip: text) {
    .palworld-dimension .dimension-title {
        color: var(--palworld-accent, #ff6b9d);
        background: none;
    }
}

.palworld-node {
    border-color: var(--palworld-accent, #ff6b9d);
}

.palworld-node::after {
    background: linear-gradient(90deg, transparent, var(--palworld-accent, #ff6b9d), transparent);
}

.palworld-node:hover {
    box-shadow: 0 0 30px rgba(255, 107, 157, 0.5);
}

.palworld-node .server-name {
    color: var(--palworld-accent, #ff6b9d);
}

/* CRITICAL FIX: Bigger server address buttons for Palworld with proper width */
.palworld-node .server-address {
    background: rgba(255, 107, 157, 0.1);
    font-size: var(--font-size-md, 1.2rem) !important;
    padding: var(--spacing-md, 1.5rem) var(--spacing-lg, 2rem) !important;
    min-height: 60px !important;
    
    /* CRITICAL FIX: Ensure full IP:Port is visible */
    white-space: nowrap;
    overflow: visible;
    min-width: 350px; /* Ensure minimum width for full IP display */
    width: fit-content;
    max-width: 480px;
    
    /* CRITICAL FIX: Better text handling for IP addresses */
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
    text-align: center;
    
    /* CRITICAL FIX: Box sizing to prevent overflow */
    box-sizing: border-box;
}

.palworld-node .server-address:hover {
    background: rgba(255, 107, 157, 0.2);
}

/* Player Count Display */
.player-count {
    margin-top: var(--spacing-sm, 1rem);
    padding: var(--spacing-xs, 0.5rem) var(--spacing-sm, 1rem);
    background: rgba(255, 107, 157, 0.1);
    border: 1px solid rgba(255, 107, 157, 0.3);
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 0.5rem);
    font-size: var(--font-size-sm, 1rem);
    transition: all var(--transition-fast, 0.3s ease);
    justify-content: center;
}

.player-count.online {
    background: rgba(0, 255, 0, 0.1);
    border-color: rgba(0, 255, 0, 0.5);
    color: #00ff00;
}

.player-count.offline {
    background: rgba(255, 0, 0, 0.1);
    border-color: rgba(255, 0, 0, 0.5);
    color: #ff6666;
}

.player-count.checking {
    background: rgba(255, 255, 0, 0.1);
    border-color: rgba(255, 255, 0, 0.5);
    color: #ffff00;
    animation: pulse-checking 2s ease-in-out infinite;
}

@keyframes pulse-checking {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

.player-icon {
    font-size: var(--font-size-md, 1.2rem);
    flex-shrink: 0;
}

.player-text {
    font-weight: bold;
    white-space: nowrap;
}

.player-count:hover {
    transform: scale(1.05);
}

/* Player count mobile responsiveness */
@media (max-width: 768px) {
    .player-count {
        font-size: var(--font-size-xs, 0.875rem);
        padding: var(--spacing-xs, 0.5rem);
    }
    
    .player-icon {
        font-size: var(--font-size-sm, 1rem);
    }
    
    /* CRITICAL FIX: Mobile palworld server address */
    .palworld-node .server-address {
        min-width: auto;
        font-size: var(--font-size-md, 1.2rem) !important;
        padding: var(--spacing-sm, 1rem) !important;
        white-space: normal;
        word-break: break-all;
    }
}

/* CRITICAL FIX: Desktop-specific adjustments */
@media (min-width: 769px) {
    .home-dimension {
        /* CRITICAL FIX: Ensure proper spacing on desktop */
        padding: calc(var(--spacing-xl, 3rem) * 1.5);
    }
    
    .logo-matrix {
        /* CRITICAL FIX: Ensure logo is properly sized and visible */
        margin-top: 0;
        margin-bottom: var(--spacing-xl, 3rem);
    }
    
    .tagline {
        margin-bottom: calc(var(--spacing-xl, 3rem) * 1.5);
    }
    
    .server-grid {
        margin-top: calc(var(--spacing-xl, 3rem) * 1.5);
        /* Keep horizontal layout on desktop */
        grid-template-columns: repeat(4, 1fr);
        max-width: 1200px;
    }
    
    .game-portal {
        /* Desktop game portals should be square and vertical layout */
        width: 250px;
        height: 250px;
        flex-direction: column;
        padding: var(--spacing-lg, 2rem);
    }
    
    .game-icon-img {
        width: 100px;
        height: 100px;
        margin-bottom: var(--spacing-md, 1.5rem);
        margin-right: 0;
    }
}

/* CRITICAL FIX: Server Settings Section with better layout */
.server-settings-container {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 1200px;
    flex-shrink: 0;
}

.settings-title {
    font-size: var(--font-size-2xl, 3rem);
    font-size: clamp(1.5rem, 4vw, 3rem);
    color: var(--palworld-accent, #ff6b9d);
    text-align: center;
    margin-bottom: var(--spacing-lg, 2rem);
    font-weight: bold;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    max-width: 100%;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-xl, 3rem);
    width: 100%;
}

/* COMPATIBILITY FIX: Grid fallback */
@supports not (display: grid) {
    .settings-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .settings-grid .settings-panel {
        width: 100%;
        max-width: 500px;
        margin: var(--spacing-sm, 1rem);
    }
}

.settings-panel {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--palworld-accent, #ff6b9d);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    background: rgba(0, 0, 0, 0.85);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    
    /* CRITICAL FIX: Better content handling */
    overflow: hidden;
    word-wrap: break-word;
}

/* COMPATIBILITY FIX: Backdrop-filter fallback */
@supports not (backdrop-filter: blur(10px)) {
    .settings-panel {
        background: rgba(0, 0, 0, 0.9);
    }
}

.settings-panel h4 {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-xl, 2rem);
    font-size: clamp(1.25rem, 3vw, 2rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-align: center;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    max-width: 100%;
}

.settings-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs, 0.5rem);
}

.setting-item {
    font-size: var(--font-size-sm, 1rem);
    padding: var(--spacing-xs, 0.5rem);
    border-radius: 5px;
    background: rgba(255, 107, 157, 0.05);
    transition: background var(--transition-fast, 0.3s ease);
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    line-height: 1.4;
}

.setting-item:hover {
    background: rgba(255, 107, 157, 0.1);
}

.setting-item strong {
    color: var(--primary-glow, #00ffff);
}

/* CRITICAL FIX: Custom Passives Section with better layout */
.custom-passives-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 1200px;
    flex-shrink: 0;
}

.passives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md, 1.5rem);
    margin-bottom: var(--spacing-lg, 2rem);
    width: 100%;
}

/* COMPATIBILITY FIX: Grid fallback */
@supports not (display: grid) {
    .passives-grid {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .passives-grid .passive-item {
        width: 100%;
        max-width: 350px;
        margin: var(--spacing-xs, 0.5rem);
    }
}

.passive-item {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid rgba(255, 107, 157, 0.5);
    border-radius: 10px;
    padding: var(--spacing-md, 1.5rem);
    display: flex;
    align-items: center;
    gap: var(--spacing-md, 1.5rem);
    transition: all var(--transition-fast, 0.3s ease);
    
    /* CRITICAL FIX: Better content handling */
    word-wrap: break-word;
    overflow: hidden;
}

.passive-item.rank-4 {
    border-color: var(--tertiary-glow, #ffff00);
}

.passive-item.rank-3 {
    border-color: var(--secondary-glow, #ff00ff);
}

.passive-item.rank-2 {
    border-color: var(--primary-glow, #00ffff);
}

.passive-item.rank-1 {
    border-color: rgba(255, 255, 255, 0.5);
}

.passive-item:hover {
    transform: translateY(-2px);
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    
    box-shadow: 0 5px 20px rgba(255, 107, 157, 0.3);
}

/* PERFORMANCE FIX: Disable expensive effects on touch devices */
@media (hover: none) {
    .passive-item:hover {
        transform: none;
        box-shadow: none;
    }
}

.passive-icon {
    font-size: var(--font-size-2xl, 3rem);
    font-size: clamp(1.5rem, 4vw, 3rem);
    flex-shrink: 0;
    
    /* PERFORMANCE FIX: Better emoji rendering */
    text-rendering: auto;
}

.passive-details {
    flex: 1;
    min-width: 0; /* CRITICAL FIX: Allow text to wrap properly */
}

.passive-name {
    font-weight: bold;
    color: var(--primary-glow, #00ffff);
    margin-bottom: var(--spacing-xs, 0.5rem);
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    line-height: 1.3;
}

.passive-desc {
    font-size: var(--font-size-sm, 1rem);
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    line-height: 1.4;
}

/* CRITICAL FIX: All passives container with proper overflow */
.all-passives-container {
    width: 100%;
    max-height: 70vh;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    
    /* CRITICAL FIX: Better scroll styling */
    scrollbar-width: thin;
    scrollbar-color: var(--primary-glow, #00ffff) rgba(0, 0, 0, 0.3);
}

.all-passives-container::-webkit-scrollbar {
    width: 8px;
}

.all-passives-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

.all-passives-container::-webkit-scrollbar-thumb {
    background: var(--primary-glow, #00ffff);
    border-radius: 4px;
}

/* Zombie Particles for 7 Days Dimension */
.zombie-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    top: 0;
    left: 0;
    z-index: 1;
}

.particle {
    position: absolute;
    color: var(--sevendays-accent, #ff6b35);
    font-size: var(--font-size-xl, 2rem);
    animation: drift 10s linear infinite;
    opacity: 0.3;
    will-change: transform;
}

@keyframes drift {
    from {
        transform: translateX(-100px) translateY(100vh) rotate(0deg);
    }
    to {
        transform: translateX(100vw) translateY(-100px) rotate(360deg);
    }
}

/* PERFORMANCE FIX: Reduce particles on mobile */
@media (max-width: 768px) {
    .particle {
        font-size: var(--font-size-lg, 1.5rem);
        opacity: 0.2;
    }
}

/* 7 Days to Die Dimension */
.sevendays-dimension {
    background: radial-gradient(ellipse at center, rgba(255, 107, 53, 0.15) 0%, transparent 70%),
                linear-gradient(180deg, #0a0a0a 0%, #2e0a1a 100%);
}

.sevendays-dimension .dimension-title {
    background: linear-gradient(45deg, var(--sevendays-accent, #ff6b35), #ff8c42);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* COMPATIBILITY FIX: Text gradient fallback */
@supports not (-webkit-background-clip: text) {
    .sevendays-dimension .dimension-title {
        color: var(--sevendays-accent, #ff6b35);
        background: none;
    }
}

/* CRITICAL FIX: 7 Days Server Node Styling - Bigger box and centered header */
.sevendays-node {
    border-color: var(--sevendays-accent, #ff6b35);
    background: rgba(255, 107, 53, 0.05);
    min-width: 450px;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.sevendays-node::after {
    background: linear-gradient(90deg, transparent, var(--sevendays-accent, #ff6b35), transparent);
}

.sevendays-node:hover {
    box-shadow: 0 0 30px rgba(255, 107, 53, 0.5);
}

/* CRITICAL FIX: Center the server name */
.sevendays-node .server-name {
    color: var(--sevendays-accent, #ff6b35);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-align: center;
    white-space: nowrap;
    overflow: visible;
}

.sevendays-node .server-address {
    color: var(--sevendays-accent, #ff6b35);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
	justify-content: center;
	white-space: nowrap;
	padding: var(--spacing-md, 1.5rem) var(--spacing-lg, 2rem);
}

/* CRITICAL FIX: Center Quick Setup Guide headers and content */
.compact-instructions {
    text-align: center;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
}

.compact-title {
    font-size: var(--font-size-xl, 2rem);
    color: var(--sevendays-accent, #ff6b35);
    text-align: center;
    margin-bottom: var(--spacing-lg, 2rem);
    font-weight: bold;
}

.instruction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg, 2rem);
    text-align: center;
}

.instruction-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--sevendays-accent, #ff6b35);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    text-align: center;
    transition: all var(--transition-fast, 0.3s ease);
}

.instruction-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}

.card-header {
    font-size: var(--font-size-lg, 1.5rem);
    font-weight: bold;
    color: var(--primary-glow, #00ffff);
    margin-bottom: var(--spacing-sm, 1rem);
    text-align: center;
}

.card-content {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    line-height: 1.5;
    text-align: center;
}

.card-content strong {
    color: var(--sevendays-accent, #ff6b35);
}

/* Quick Links Section */
.quick-links-section {
    margin: var(--spacing-lg, 2rem) 0;
    width: 100%;
    max-width: 875px;
}

.quick-link-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md, 1.5rem);
    justify-content: center;
}

.link-btn {
    background: var(--sevendays-accent, #ff6b35);
    color: #000;
    border: 2px solid var(--sevendays-accent, #ff6b35);
    font-weight: bold;
    white-space: nowrap;
    padding: var(--spacing-sm, 1rem);
}

.link-btn:hover {
    background: #ff8c42;
    color: #000;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
}

/* Project Zomboid Dimension */
.zomboid-dimension {
    background: radial-gradient(ellipse at center, rgba(255, 107, 107, 0.15) 0%, transparent 70%),
                linear-gradient(180deg, #0a0a0a 0%, #1a0a2e 100%);
}

.zomboid-dimension .dimension-title {
    background: linear-gradient(45deg, var(--zomboid-accent, #ff6b6b), #ff8787);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* COMPATIBILITY FIX: Text gradient fallback */
@supports not (-webkit-background-clip: text) {
    .zomboid-dimension .dimension-title {
        color: var(--zomboid-accent, #ff6b6b);
        background: none;
    }
}

/* Zomboid Server Node Styling */
.zomboid-node {
    border-color: var(--zomboid-accent, #ff6b6b);
    background: rgba(255, 107, 107, 0.05);
}

.zomboid-node::after {
    background: linear-gradient(90deg, transparent, var(--zomboid-accent, #ff6b6b), transparent);
}

.zomboid-node:hover {
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.5);
}

.zomboid-node .server-name {
    color: var(--zomboid-accent, #ff6b6b);
    font-size: var(--font-size-lg, 1.5rem);
}

.zomboid-node .server-address {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
}

.zomboid-node .server-address:hover {
    background: rgba(255, 107, 107, 0.2);
}

/* Zomboid Setup Section */
.zomboid-setup-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 800px;
}

.setup-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--zomboid-accent, #ff6b6b);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-lg, 2rem);
}

.setup-header {
    font-size: var(--font-size-lg, 1.5rem);
    font-weight: bold;
    color: var(--primary-glow, #00ffff);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-align: center;
}

.setup-steps {
    margin-bottom: var(--spacing-md, 1.5rem);
}

.setup-step {
    padding: var(--spacing-sm, 1rem);
    margin-bottom: var(--spacing-xs, 0.5rem);
    background: rgba(255, 107, 107, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--zomboid-accent, #ff6b6b);
    font-size: var(--font-size-md, 1.2rem);
}

.setup-step strong {
    color: var(--zomboid-accent, #ff6b6b);
}

.inline-link-btn {
    background: transparent;
    border: 1px solid var(--primary-glow, #00ffff);
    color: var(--primary-glow, #00ffff);
    padding: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: inherit;
    text-decoration: none;
    transition: all var(--transition-fast, 0.3s ease);
}

.inline-link-btn:hover {
    background: var(--primary-glow, #00ffff);
    color: #000;
}

.setup-note {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    border-radius: 8px;
    padding: var(--spacing-sm, 1rem);
    font-style: italic;
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
}

/* Economy Section */
.economy-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 800px;
}

.economy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md, 1.5rem);
    margin-bottom: var(--spacing-lg, 2rem);
}

.economy-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--zomboid-accent, #ff6b6b);
    border-radius: 10px;
    padding: var(--spacing-md, 1.5rem);
    text-align: center;
    transition: all var(--transition-fast, 0.3s ease);
}

.economy-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.economy-header {
    font-size: var(--font-size-lg, 1.5rem);
    font-weight: bold;
    color: var(--primary-glow, #00ffff);
    margin-bottom: var(--spacing-sm, 1rem);
}

.economy-content {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    line-height: 1.5;
}

.economy-content strong {
    color: var(--zomboid-accent, #ff6b6b);
    background: rgba(255, 107, 107, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.economy-content .highlight {
    color: var(--primary-glow, #00ffff);
    font-weight: bold;
    display: block;
    margin-top: var(--spacing-xs, 0.5rem);
}

/* Community Section */
.community-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 600px;
}

.community-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--zomboid-accent, #ff6b6b);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    text-align: center;
}

.community-content {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    margin-bottom: var(--spacing-lg, 2rem);
    font-size: var(--font-size-md, 1.2rem);
    line-height: 1.6;
}

.community-btn {
    background: var(--zomboid-accent, #ff6b6b);
    color: #000;
    border: 2px solid var(--zomboid-accent, #ff6b6b);
    font-weight: bold;
}

.community-btn:hover {
    background: #ff8787;
    color: #000;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
}

/* Mobile responsiveness for Zomboid page */
@media (max-width: 768px) {
    .economy-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm, 1rem);
    }
    
    .economy-card,
    .setup-card,
    .community-card {
        padding: var(--spacing-md, 1.5rem);
    }
    
    .setup-step {
        font-size: var(--font-size-sm, 1rem);
    }
}

/* Dune: Awakening Dimension with desert theme */
.dune-dimension {
    background: radial-gradient(ellipse at center, rgba(255, 181, 53, 0.15) 0%, transparent 70%),
                linear-gradient(180deg, #0a0a0a 0%, #2e1a0a 100%);
}

.dune-dimension .dimension-title {
    background: linear-gradient(45deg, var(--dune-accent, #ffb535), #ffc942);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* COMPATIBILITY FIX: Text gradient fallback */
@supports not (-webkit-background-clip: text) {
    .dune-dimension .dimension-title {
        color: var(--dune-accent, #ffb535);
        background: none;
    }
}

/* CSS Variables for Dune theme */
:root {
    --dune-accent: #ffb535;
    --dune-secondary: #ffc942;
    --dune-dark: #8b4513;
}

/* Sand Particles for Dune Dimension */
.sand-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    top: 0;
    left: 0;
    z-index: 1;
}

.sand-particle {
    position: absolute;
    color: var(--dune-accent, #ffb535);
    font-size: var(--font-size-xl, 2rem);
    animation: sandstorm 12s linear infinite;
    opacity: 0.4;
    will-change: transform;
}

@keyframes sandstorm {
    from {
        transform: translateX(-100px) translateY(100vh) rotate(0deg);
    }
    to {
        transform: translateX(100vw) translateY(-100px) rotate(360deg);
    }
}

/* PERFORMANCE FIX: Reduce particles on mobile */
@media (max-width: 768px) {
    .sand-particle {
        font-size: var(--font-size-lg, 1.5rem);
        opacity: 0.3;
    }
}

/* CRITICAL FIX: Dune Server Node Styling - Wider and centered */
.dune-node {
    border-color: var(--dune-accent, #ffb535);
    background: rgba(255, 181, 53, 0.05);
    min-width: 600px;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.dune-node::after {
    background: linear-gradient(90deg, transparent, var(--dune-accent, #ffb535), transparent);
}

.dune-node:hover {
    box-shadow: 0 0 30px rgba(255, 181, 53, 0.5);
}

.dune-node .server-name {
    color: var(--dune-accent, #ffb535);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
}

/* Dune Overview Section */
.dune-overview-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 1000px;
}

.overview-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--dune-accent, #ffb535);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.overview-card p {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    margin-bottom: var(--spacing-md, 1.5rem);
    font-size: var(--font-size-md, 1.2rem);
    line-height: 1.6;
}

.overview-card p:last-child {
    margin-bottom: 0;
}

.overview-card strong {
    color: var(--dune-accent, #ffb535);
}

/* Server Status Section */
.server-status-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 800px;
}

.server-details {
    margin: var(--spacing-md, 1.5rem) 0;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm, 1rem);
    padding: var(--spacing-xs, 0.5rem) 0;
    border-bottom: 1px solid rgba(255, 181, 53, 0.2);
}

.detail-label {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    font-size: var(--font-size-sm, 1rem);
}

.detail-value {
    color: var(--primary-glow, #00ffff);
    font-weight: bold;
    font-size: var(--font-size-sm, 1rem);
}

.server-var {
    color: var(--dune-accent, #ffb535);
    background: rgba(255, 181, 53, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.server-status-widget {
    margin: var(--spacing-md, 1.5rem) 0;
    padding: var(--spacing-md, 1.5rem);
    background: rgba(255, 181, 53, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 181, 53, 0.3);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 1rem);
    margin-bottom: var(--spacing-sm, 1rem);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #00ff00;
    animation: pulse-online 2s ease-in-out infinite;
}

.status-indicator.online .status-dot {
    background: #00ff00;
}

.status-indicator.offline .status-dot {
    background: #ff0000;
    animation: none;
}

@keyframes pulse-online {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.status-text {
    font-weight: bold;
    color: #00ff00;
}

.status-indicator.offline .status-text {
    color: #ff6666;
}

.join-now-btn {
    width: 100%;
    background: var(--dune-accent, #ffb535);
    color: #000;
    border: 2px solid var(--dune-accent, #ffb535);
    font-weight: bold;
    font-size: var(--font-size-lg, 1.5rem);
    padding: var(--spacing-md, 1.5rem);
    margin-top: var(--spacing-md, 1.5rem);
}

.join-now-btn:hover {
    background: var(--dune-secondary, #ffc942);
    color: #000;
    box-shadow: 0 0 25px rgba(255, 181, 53, 0.8);
    transform: translateY(-2px);
}

/* Connection Section */
.connection-section {
    margin-top: var(--spacing-xl, 3rem);
    width: 100%;
    max-width: 1000px;
}

.connection-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-xl, 3rem);
}

.connection-method {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--dune-accent, #ffb535);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.method-header {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-align: center;
}

.connection-steps {
    margin-bottom: var(--spacing-md, 1.5rem);
}

.connection-step {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    padding: var(--spacing-sm, 1rem);
    background: rgba(255, 181, 53, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--dune-accent, #ffb535);
}

.step-number {
    background: var(--dune-accent, #ffb535);
    color: #000;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: var(--font-size-md, 1.2rem);
    flex-shrink: 0;
}

.step-content {
    flex: 1;
    font-size: var(--font-size-md, 1.2rem);
    line-height: 1.5;
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
}

.step-content strong {
    color: var(--primary-glow, #00ffff);
}

.direct-connect-info {
    text-align: center;
}

.direct-connect-info p {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    margin-bottom: var(--spacing-sm, 1rem);
}

.connect-address {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    padding: var(--spacing-sm, 1rem);
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    color: var(--primary-glow, #00ffff);
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    margin-bottom: var(--spacing-sm, 1rem);
    user-select: all;
    -webkit-user-select: all;
    -moz-user-select: all;
}

.connect-address:hover {
    background: rgba(0, 255, 255, 0.2);
    border-color: rgba(0, 255, 255, 0.5);
    transform: scale(1.02);
}

/* Discord Dimension */
.discord-dimension {
    background: radial-gradient(ellipse at center, rgba(0, 212, 255, 0.15) 0%, transparent 70%),
                linear-gradient(180deg, #0a0a0a 0%, #0a1a2e 100%);
}

.discord-dimension .dimension-title {
    background: linear-gradient(45deg, var(--discord-accent, #00d4ff), #33e1ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* COMPATIBILITY FIX: Text gradient fallback */
@supports not (-webkit-background-clip: text) {
    .discord-dimension .dimension-title {
        color: var(--discord-accent, #00d4ff);
        background: none;
    }
}

/* Discord Feature Cards */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-xl, 3rem);
}

.feature-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--discord-accent, #00d4ff);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    text-align: center;
    transition: all var(--transition-fast, 0.3s ease);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
}

.feature-icon {
    font-size: var(--font-size-3xl, 4rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    color: var(--discord-accent, #00d4ff);
}

.feature-details h4 {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-sm, 1rem);
}

.feature-details p {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    line-height: 1.5;
}

/* Discord Commands Section */
.commands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-xl, 3rem);
}

.command-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--discord-accent, #00d4ff);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.command-header {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-align: center;
    font-weight: bold;
}

.command-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 1rem);
}

.command-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm, 1rem);
    background: rgba(0, 212, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--discord-accent, #00d4ff);
}

.command-name {
    font-family: 'Courier New', monospace;
    color: var(--discord-accent, #00d4ff);
    font-weight: bold;
    background: rgba(0, 212, 255, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.command-desc {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    font-size: var(--font-size-sm, 1rem);
}

/* Discord Stats Section */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-xl, 3rem);
}

.stat-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--discord-accent, #00d4ff);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    text-align: center;
    transition: all var(--transition-fast, 0.3s ease);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.3);
}

.stat-number {
    font-size: var(--font-size-3xl, 4rem);
    font-weight: bold;
    color: var(--discord-accent, #00d4ff);
    margin-bottom: var(--spacing-xs, 0.5rem);
    background: linear-gradient(45deg, var(--discord-accent, #00d4ff), #33e1ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    font-size: var(--font-size-sm, 1rem);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Discord Join Section */
.discord-join-card {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--discord-accent, #00d4ff);
    border-radius: 15px;
    padding: var(--spacing-xl, 3rem);
    text-align: center;
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.discord-content {
    margin-bottom: var(--spacing-xl, 3rem);
}

.discord-content p {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    font-size: var(--font-size-md, 1.2rem);
    line-height: 1.6;
    margin-bottom: var(--spacing-md, 1.5rem);
}

.discord-content p:last-child {
    margin-bottom: 0;
}

.discord-buttons {
    display: flex;
    gap: var(--spacing-lg, 2rem);
    justify-content: center;
    flex-wrap: wrap;
}

.discord-btn {
    font-size: var(--font-size-lg, 1.5rem);
    padding: var(--spacing-md, 1.5rem) var(--spacing-xl, 3rem);
}

.discord-btn.primary {
    background: var(--discord-accent, #00d4ff);
    color: #000;
    border: 2px solid var(--discord-accent, #00d4ff);
}

.discord-btn.primary:hover {
    background: #33e1ff;
    color: #000;
    box-shadow: 0 0 25px rgba(0, 212, 255, 0.8);
}

.discord-btn.secondary {
    background: transparent;
    color: var(--discord-accent, #00d4ff);
    border: 2px solid var(--discord-accent, #00d4ff);
}

.discord-btn.secondary:hover {
    background: var(--discord-accent, #00d4ff);
    color: #000;
}

/* CRITICAL FIX: Server Matrix Layout with better responsiveness */
.server-matrix {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl, 3rem);
    width: 95%;
    max-width: 1600px;
    margin: 0 auto;
}

/* COMPATIBILITY FIX: Grid fallback */
@supports not (display: grid) {
    .server-matrix {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .server-matrix .server-node {
		min-width: 400px;
        max-width: 700px;
        margin: var(--spacing-md, 1.3rem);
    }
}

/* CRITICAL FIX: Mobile-specific overrides */
@media (max-width: 768px) {
    .game-dimension {
        padding: var(--spacing-sm, 1rem);
    }
    
    .game-dimension .dimension-content {
        padding-top: 0;
        padding-bottom: max(80px, env(safe-area-inset-bottom, 0) + 80px);
        margin-top: 0;
        margin-bottom: 0;
    }
    
    .game-dimension .dimension-header {
        margin-top: max(60px, env(safe-area-inset-top, 0) + 60px);
        margin-bottom: var(--spacing-lg, 2rem);
    }
    
    .dimension-content {
        padding: 0;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md, 1.5rem);
    }
    
    .passives-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm, 1rem);
    }
    
    .server-matrix {
        grid-template-columns: 1fr;
        width: 100%;
        gap: var(--spacing-md, 1.5rem);
    }
    
    .all-passives-container {
        max-height: 50vh;
    }
    
    /* CRITICAL FIX: Center home page grid on mobile */
    .server-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md, 1.5rem);
        max-width: 400px;
    }
    
    .server-grid .game-portal:nth-child(4) {
        grid-column: auto;
    }
    
    /* Mobile specific server sizing */
    .sevendays-node,
    .dune-node {
        min-width: auto;
        max-width: 100%;
    }
}

/* PERFORMANCE FIX: Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .logo-matrix,
    .tagline {
        animation: none;
    }
    
    .passive-item:hover {
        transform: none;
    }
}

/* Section titles consistent styling */
.section-title {
    font-size: var(--font-size-2xl, 3rem);
    font-size: clamp(1.5rem, 4vw, 3rem);
    text-align: center;
    margin-bottom: var(--spacing-lg, 2rem);
    font-weight: bold;
    text-transform: uppercase;
    word-wrap: break-word;
    max-width: 100%;
}

/* Discord specific styling */
.discord-overview-section .section-title {
    color: var(--discord-accent, #00d4ff);
}

.discord-features-section .section-title {
    color: var(--primary-glow, #00ffff);
}

.discord-commands-section .section-title {
    color: var(--discord-accent, #00d4ff);
}

.discord-stats-section .section-title {
    color: var(--primary-glow, #00ffff);
}

.join-discord-section .section-title {
    color: var(--discord-accent, #00d4ff);
}

/* COMPATIBILITY FIX: Backdrop-filter fallback */
@supports not (backdrop-filter: blur(10px)) {
    .feature-card,
    .command-card,
    .stat-card,
    .discord-join-card,
    .connection-method,
    .overview-card {
        background: rgba(0, 0, 0, 0.9);
    }
}