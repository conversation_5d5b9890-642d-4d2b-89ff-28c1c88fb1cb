/* styles/components.css - Reusable component styles with compatibility fixes */

/* CRITICAL FIX: Z-index management system */
:root {
    --z-matrix: -1;
    --z-content: 1;
    --z-navigation: 1000;
    --z-indicators: 1000;
    --z-modals: 10001;
    --z-loading: 10002;
    --z-cursor: 10000;
}

/* Navigation Portal with browser compatibility */
.nav-portal {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: var(--z-navigation, 1000);
    display: flex;
    gap: 10px;
    
    /* COMPATIBILITY FIX: Flexbox fallback */
    align-items: center;
}

/* COMPATIBILITY FIX: Flexbox fallback for older browsers */
@supports not (display: flex) {
    .nav-portal {
        display: block;
    }
    
    .nav-portal .nav-orb {
        display: inline-block;
        margin: 0 5px;
        vertical-align: middle;
    }
}

.nav-orb {
    width: 60px;
    height: 60px;
    border-radius: 50%;

    /* CRITICAL FIX: Backdrop-filter fallback - Updated for Orange/Red Theme */
    background: rgba(255, 102, 0, 0.8);
    background: radial-gradient(circle at 30% 30%, rgba(255, 102, 0, 0.8), rgba(255, 102, 0, 0.1));

    border: 2px solid var(--primary-glow, #ff6600);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    
    /* PERFORMANCE FIX: Better transitions */
    transition: all var(--transition-fast, 0.3s ease);
    
    /* CRITICAL FIX: Better shadow with fallbacks - Updated for Orange/Red Theme */
    box-shadow: 0 0 30px rgba(255, 102, 0, 0.5);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    
    /* PERFORMANCE FIX: Better compositing */
    will-change: transform;
    backface-visibility: hidden;
    
    /* ACCESSIBILITY FIX: Better touch targets */
    min-height: 44px;
    min-width: 44px;
}

/* COMPATIBILITY FIX: Backdrop-filter fallback - Updated for Orange/Red Theme */
@supports not (backdrop-filter: blur(10px)) {
    .nav-orb {
        background: rgba(255, 102, 0, 0.9);
    }
}

.nav-orb:hover {
    transform: scale(1.2) rotate(360deg);
    box-shadow: 0 0 50px var(--primary-glow, #ff6600);
}

.nav-orb.active {
    background: rgba(255, 51, 0, 0.8);
    background: radial-gradient(circle at 30% 30%, rgba(255, 51, 0, 0.8), rgba(255, 51, 0, 0.1));
    border-color: var(--secondary-glow, #ff3300);
    box-shadow: 0 0 50px var(--secondary-glow, #ff3300);
}

/* PERFORMANCE FIX: Disable hover effects on touch devices - Updated for Orange/Red Theme */
@media (hover: none) {
    .nav-orb:hover {
        transform: none;
        box-shadow: 0 0 30px rgba(255, 102, 0, 0.5);
    }
}

/* Dimension Indicator with better accessibility */
.dimension-indicator {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: var(--z-indicators, 1000);
    
    /* ACCESSIBILITY FIX: Better focus management */
    padding: 10px;
}

.indicator-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    
    /* ACCESSIBILITY FIX: Better touch targets */
    position: relative;
    min-height: 44px;
    min-width: 44px;
    
    /* CRITICAL FIX: Center the actual dot */
    display: flex;
    align-items: center;
    justify-content: center;
}

.indicator-dot::before {
    content: '';
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all var(--transition-fast, 0.3s ease);
}

.indicator-dot.active::before {
    background: var(--primary-glow, #ff6600);
    box-shadow: 0 0 20px var(--primary-glow, #ff6600);
    transform: scale(1.5);
}

/* Quantum Button with better cross-browser support - Updated for Orange/Red Theme */
.quantum-button {
    background: transparent;
    border: 2px solid var(--primary-glow, #ff6600);
    color: var(--primary-glow, #ff6600);
    padding: var(--spacing-sm, 1rem) var(--spacing-lg, 2rem);
    font-family: inherit;
    font-size: var(--font-size-sm, 1rem);
    font-weight: bold;
    cursor: pointer;
    border-radius: 5px;
    
    /* PERFORMANCE FIX: Better transitions */
    transition: all var(--transition-fast, 0.3s ease);
    
    position: relative;
    overflow: hidden;
    margin: var(--spacing-xs, 0.5rem);
    
    /* PERFORMANCE FIX: Better compositing */
    will-change: transform;
    backface-visibility: hidden;
    
    /* ACCESSIBILITY FIX: Better touch targets */
    min-height: 44px;
    min-width: 44px;
    
    /* CRITICAL FIX: Better text handling */
    text-align: center;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.quantum-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-glow, #ff6600);
    
    /* COMPATIBILITY FIX: Transform fallbacks */
    transform: scaleX(0);
    -webkit-transform: scaleX(0);
    -moz-transform: scaleX(0);
    
    transform-origin: right;
    -webkit-transform-origin: right;
    -moz-transform-origin: right;
    
    transition: transform var(--transition-fast, 0.3s ease);
    z-index: -1;
}

.quantum-button:hover::before {
    transform: scaleX(1);
    -webkit-transform: scaleX(1);
    -moz-transform: scaleX(1);
    
    transform-origin: left;
    -webkit-transform-origin: left;
    -moz-transform-origin: left;
}

.quantum-button:hover {
    color: var(--bg-dark, #0a0a0a);
    box-shadow: 0 0 30px var(--primary-glow, #ff6600);
    transform: translateY(-2px);
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
}

/* PERFORMANCE FIX: Disable expensive effects on touch devices - Updated for Orange/Red Theme */
@media (hover: none) {
    .quantum-button:hover {
        transform: none;
        box-shadow: none;
        color: var(--primary-glow, #ff6600);
    }
    
    .quantum-button:hover::before {
        transform: scaleX(0);
    }
    
    /* Add active state for touch feedback - Updated for Orange/Red Theme */
    .quantum-button:active {
        transform: scale(0.95);
        background: rgba(255, 102, 0, 0.1);
    }
}

/* Game Portal Cards with performance optimizations - Updated for Orange/Red Theme */
.game-portal {
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--primary-glow, #ff6600);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    /* PERFORMANCE FIX: Better transitions */
    transition: all var(--transition-medium, 0.5s ease);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    background: rgba(0, 0, 0, 0.9);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    
    position: relative;
    overflow: hidden;
    
    /* PERFORMANCE FIX: Better compositing */
    will-change: transform;
    backface-visibility: hidden;
    
    /* ACCESSIBILITY FIX: Better touch targets */
    min-height: 44px;
    min-width: 44px;
}

/* COMPATIBILITY FIX: Backdrop-filter fallback */
@supports not (backdrop-filter: blur(10px)) {
    .game-portal {
        background: rgba(0, 0, 0, 0.95);
    }
}

.game-portal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    
    /* CRITICAL FIX: Better gradient handling - Updated for Orange/Red Theme */
    background: linear-gradient(45deg, transparent, rgba(255, 102, 0, 0.1), transparent);
    
    /* COMPATIBILITY FIX: Transform fallbacks */
    transform: translateX(-100%);
    -webkit-transform: translateX(-100%);
    -moz-transform: translateX(-100%);
    
    transition: transform 0.6s ease;
    -webkit-transition: -webkit-transform 0.6s ease;
    -moz-transition: -moz-transform 0.6s ease;
}

.game-portal:hover::before {
    transform: translateX(100%);
    -webkit-transform: translateX(100%);
    -moz-transform: translateX(100%);
}

.game-portal:hover {
    transform: translateY(-20px) scale(1.1);
    -webkit-transform: translateY(-20px) scale(1.1);
    -moz-transform: translateY(-20px) scale(1.1);
    
    box-shadow: 0 20px 60px rgba(255, 102, 0, 0.4);
    border-color: var(--secondary-glow, #ff3300);
}

/* PERFORMANCE FIX: Disable expensive effects on touch devices */
@media (hover: none) {
    .game-portal:hover {
        transform: none;
        box-shadow: 0 0 20px rgba(255, 102, 0, 0.3);
    }
    
    .game-portal:hover::before {
        transform: translateX(-100%);
    }
    
    .game-portal:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

.game-icon {
    font-size: var(--font-size-3xl, 4rem);
    margin-bottom: var(--spacing-sm, 1rem);
    
    /* CRITICAL FIX: Better icon rendering */
    filter: drop-shadow(0 0 10px currentColor);
    -webkit-filter: drop-shadow(0 0 10px currentColor);
    
    /* PERFORMANCE FIX: Better text rendering */
    text-rendering: auto;
}

.game-title {
    font-weight: bold;
    font-size: var(--font-size-md, 1.2rem);
    text-align: center;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    hyphens: auto;
}

/* Server Node with better browser support - Updated for Orange/Red Theme */
.server-node {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid var(--primary-glow, #ff6600);
    border-radius: 15px;
    padding: var(--spacing-lg, 2rem);
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    background: rgba(0, 0, 0, 0.85);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    
    transition: all var(--transition-fast, 0.3s ease);
    position: relative;
    overflow: hidden;
    
    /* PERFORMANCE FIX: Better compositing */
    will-change: transform;
    backface-visibility: hidden;
    
    /* CRITICAL FIX: Consistent sizing */
    width: 100%;
    max-width: 500px;
    min-width: 300px; /* Reduced from 400px to prevent horizontal overflow */
    margin: 0 auto; /* Center the node */
}

.server-node::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;

    
    /* CRITICAL FIX: Better gradient handling - Updated for Orange/Red Theme */
    background: linear-gradient(90deg, transparent, var(--primary-glow, #ff6600), transparent);
    
    /* PERFORMANCE FIX: Better animation */
    animation: scanline 2s linear infinite;
    will-change: transform;
}

@keyframes scanline {
    0% { 
        transform: translateX(-100%);
        -webkit-transform: translateX(-100%);
    }
    100% { 
        transform: translateX(100%);
        -webkit-transform: translateX(100%);
    }
}

.server-node:hover {
    transform: scale(1.05);
    -webkit-transform: scale(1.05);
    -moz-transform: scale(1.05);
    
    box-shadow: 0 0 30px rgba(255, 102, 0, 0.5);
}

/* PERFORMANCE FIX: Disable expensive effects on touch devices */
@media (hover: none) {
    .server-node:hover {
        transform: none;
    }
}

/* MOBILE FIX: Responsive server node sizing */
@media (max-width: 768px) {
    .server-node {
        min-width: 280px; /* Further reduced for mobile */
        max-width: 95vw; /* Ensure it never exceeds viewport width */
        margin: 1rem auto;
        padding: 1rem;
    }
}

.server-name {
    font-size: var(--font-size-lg, 1.5rem);
    color: var(--primary-glow, #ff6600);
    margin-bottom: var(--spacing-sm, 1rem);
    font-weight: bold;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    hyphens: auto;
}

.server-address {
    font-family: 'Courier New', monospace;
    background: rgba(0, 0, 0, 0.9); /* CRITICAL FIX: Dark background for readability */
    color: #ffffff; /* CRITICAL FIX: White text for contrast */
    padding: var(--spacing-sm, 1rem) var(--spacing-md, 1.5rem);
    border-radius: 8px;
    border: 2px solid var(--primary-glow, #ff6600);
    margin: var(--spacing-sm, 1rem) 0;
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    font-size: var(--font-size-md, 1.2rem);
    text-align: center;
    font-weight: bold; /* CRITICAL FIX: Bold for better readability */
    letter-spacing: 0.5px; /* CRITICAL FIX: Better spacing */
    text-shadow: 0 0 10px rgba(255, 102, 0, 0.5); /* CRITICAL FIX: Glow effect */
    box-shadow: inset 0 0 20px rgba(255, 102, 0, 0.1); /* CRITICAL FIX: Inner glow */
    
    /* ACCESSIBILITY FIX: Better touch targets */
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    /* CRITICAL FIX: Better text handling */
    word-break: break-all;
    user-select: all;
    -webkit-user-select: all;
    -moz-user-select: all;
    
    /* Ensure button appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    
    /* Better text rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.server-address:hover {
    background: rgba(255, 102, 0, 0.2);
    color: #ffffff; /* Keep white text on hover */
    transform: scale(1.02);
    box-shadow: 0 0 30px rgba(255, 102, 0, 0.4),
                inset 0 0 20px rgba(255, 102, 0, 0.2);
    text-shadow: 0 0 15px rgba(255, 102, 0, 0.8);
}

.server-address:active {
    transform: scale(0.98);
}

/* New Image Styles for Orange/Red Theme Redesign */

/* Header Banner Image */
.header-banner {
    max-width: 100%;
    height: auto;
    margin: 0 auto 2rem auto;
    display: block;
    border-radius: 15px;
    box-shadow: 0 0 30px rgba(255, 102, 0, 0.3);
    transition: all var(--transition-fast, 0.3s ease);
    filter: drop-shadow(0 0 20px rgba(255, 102, 0, 0.2));
}

.header-banner:hover {
    box-shadow: 0 0 50px rgba(255, 102, 0, 0.5);
    transform: scale(1.02);
}

/* Main Logo */
.main-logo-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: var(--z-navigation, 1000);
    width: 80px;
    height: 80px;
    overflow: hidden;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--primary-glow, #ff6600);
    box-shadow: 0 0 20px rgba(255, 102, 0, 0.3);
    transition: all var(--transition-fast, 0.3s ease);
}

.main-logo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all var(--transition-fast, 0.3s ease);
}

.main-logo-container:hover {
    transform: scale(1.1);
    box-shadow: 0 0 30px rgba(255, 102, 0, 0.5);
}

/* Featured Section */
.featured-section {
    margin: 3rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Promotional Flyer */
.promotional-flyer {
    max-width: 300px;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 0 40px rgba(255, 102, 0, 0.4);
    transition: all var(--transition-medium, 0.5s ease);
    filter: drop-shadow(0 0 25px rgba(255, 102, 0, 0.3));
    border: 3px solid var(--primary-glow, #ff6600);
}

.promotional-flyer:hover {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 0 60px rgba(255, 102, 0, 0.6);
    border-color: var(--secondary-glow, #ff3300);
}

/* Responsive Design for New Images */
@media (max-width: 768px) {
    .header-banner {
        margin: 1rem auto;
        border-radius: 10px;
    }

    .main-logo-container {
        width: 60px;
        height: 60px;
        top: 15px;
        left: 15px;
    }

    .promotional-flyer {
        max-width: 250px;
        margin: 1rem 0;
    }

    .featured-section {
        margin: 2rem 0;
    }
}

@media (max-width: 480px) {
    .header-banner {
        margin: 0.5rem auto;
    }

    .main-logo-container {
        width: 50px;
        height: 50px;
        top: 10px;
        left: 10px;
    }

    .promotional-flyer {
        max-width: 200px;
    }
}

/* Focus state for accessibility */
.server-address:focus {
    outline: 3px solid var(--primary-glow, #00ffff);
    outline-offset: 2px;
}

.server-info {
    font-size: var(--font-size-sm, 1rem);
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    text-align: center;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    hyphens: auto;
}

/* Loading Screen with better browser support */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-darker, #050505);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading, 10002);
    
    /* PERFORMANCE FIX: Better transitions */
    transition: opacity var(--transition-medium, 0.5s ease);
    
    /* COMPATIBILITY FIX: Flexbox fallback */
    text-align: center;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 80px;
    height: 80px;
    border: 4px solid rgba(0, 255, 255, 0.3);
    border-top: 4px solid var(--primary-glow, #00ffff);
    border-radius: 50%;
    
    /* PERFORMANCE FIX: Better animation */
    animation: spin 1s linear infinite;
    will-change: transform;
    
    margin-bottom: var(--spacing-lg, 2rem);
}

@keyframes spin {
    0% { 
        transform: rotate(0deg);
        -webkit-transform: rotate(0deg);
    }
    100% { 
        transform: rotate(360deg);
        -webkit-transform: rotate(360deg);
    }
}

.loading-text {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-lg, 1.5rem);
    font-weight: bold;
    
    /* PERFORMANCE FIX: Better animation */
    animation: pulse 2s ease-in-out infinite;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
    max-width: 90%;
    text-align: center;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 1; }
}

/* Offline Indicator with better styling */
.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    
    /* CRITICAL FIX: Better gradient handling */
    background: linear-gradient(45deg, #ff4757, #ff6b6b);
    
    color: white;
    text-align: center;
    padding: 10px;
    transform: translateY(-100%);
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    
    transition: transform var(--transition-fast, 0.3s ease);
    -webkit-transition: -webkit-transform var(--transition-fast, 0.3s ease);
    -moz-transition: -moz-transform var(--transition-fast, 0.3s ease);
    
    z-index: var(--z-modals, 10001);
    font-weight: bold;
}

.offline-indicator.show {
    transform: translateY(0);
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
}

/* Install Prompt with better mobile support */
.install-prompt {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    
    /* CRITICAL FIX: Better styling */
    background: rgba(0, 255, 255, 0.9);
    color: #000;
    padding: var(--spacing-sm, 1rem);
    border-radius: 10px;
    
    /* COMPATIBILITY FIX: Backdrop-filter with fallback */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    
    transform: translateY(100%);
    -webkit-transform: translateY(100%);
    -moz-transform: translateY(100%);
    
    transition: transform var(--transition-fast, 0.3s ease);
    -webkit-transition: -webkit-transform var(--transition-fast, 0.3s ease);
    -moz-transition: -moz-transform var(--transition-fast, 0.3s ease);
    
    z-index: var(--z-modals, 10001);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    max-width: 600px;
    margin: 0 auto;
    
    /* CRITICAL FIX: Better text handling */
    word-wrap: break-word;
}

.install-prompt.show {
    transform: translateY(0);
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
}

.install-btn {
    background: #000;
    color: var(--primary-glow, #00ffff);
    border: none;
    padding: var(--spacing-xs, 0.5rem) var(--spacing-sm, 1rem);
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    margin-left: var(--spacing-sm, 1rem);
    
    /* ACCESSIBILITY FIX: Better touch targets */
    min-height: 44px;
    min-width: 44px;
}

.close-btn {
    background: none;
    border: none;
    color: #000;
    font-size: var(--font-size-lg, 1.5rem);
    cursor: pointer;
    padding: 0 var(--spacing-xs, 0.5rem);
    
    /* ACCESSIBILITY FIX: Better touch targets */
    min-height: 44px;
    min-width: 44px;
}

/* PERFORMANCE FIX: Glitch Effect optimization */
.glitch {
    animation: glitch 2s infinite;
    will-change: transform;
}

@keyframes glitch {
    0%, 100% { 
        transform: translate(0);
        -webkit-transform: translate(0);
    }
    10% { 
        transform: translate(-2px, 2px);
        -webkit-transform: translate(-2px, 2px);
    }
    20% { 
        transform: translate(2px, -2px);
        -webkit-transform: translate(2px, -2px);
    }
    30% { 
        transform: translate(-2px, -2px);
        -webkit-transform: translate(-2px, -2px);
    }
    40% { 
        transform: translate(2px, 2px);
        -webkit-transform: translate(2px, 2px);
    }
    50% { 
        transform: translate(-2px, 2px);
        -webkit-transform: translate(-2px, 2px);
    }
    60% { 
        transform: translate(2px, -2px);
        -webkit-transform: translate(2px, -2px);
    }
    70% { 
        transform: translate(-2px, -2px);
        -webkit-transform: translate(-2px, -2px);
    }
    80% { 
        transform: translate(2px, 2px);
        -webkit-transform: translate(2px, 2px);
    }
    90% { 
        transform: translate(-2px, 2px);
        -webkit-transform: translate(-2px, 2px);
    }
}

/* PERFORMANCE FIX: Disable expensive animations on low-end devices */
@media (prefers-reduced-motion: reduce) {
    .glitch,
    .loading-spinner {
        animation: none;
    }
    
    .server-node::after {
        animation: none;
    }
}

/* CRITICAL FIX: High contrast mode support */
@media (prefers-contrast: high) {
    .nav-orb,
    .game-portal,
    .server-node,
    .quantum-button {
        border-width: 3px;
        border-color: #ffffff;
    }
    
    .loading-text,
    .server-name {
        color: #ffffff;
    }
}