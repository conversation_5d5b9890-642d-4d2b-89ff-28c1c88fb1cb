/* styles/responsive.css - Mobile and responsive styles with critical fixes */

/* CRITICAL FIX: Better breakpoint strategy */
/* Large tablets and small desktops */
@media (max-width: 1200px) {
    .server-matrix {
        width: 95%;
        gap: var(--spacing-md, 1.5rem);
    }
    
    .discord-grid {
        gap: var(--spacing-md, 1.5rem);
    }
    
    /* PERFORMANCE FIX: Reduce particle effects on larger tablets */
    .zombie-particles .particle,
    .sand-particles .sand-particle {
        opacity: 0.2;
    }
}

/* Standard tablets */
@media (max-width: 1024px) {
    .server-matrix {
        width: 90%;
        grid-template-columns: 1fr;
    }
    
    .discord-grid {
        gap: var(--spacing-lg, 2rem);
    }
    
    /* PERFORMANCE FIX: Disable parallax on tablets */
    .game-portal {
        transform: none !important;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md, 1.5rem);
    }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
    /* Note: Core mobile styles are now handled in main.css for better maintainability */

    /* Keep only unique mobile-specific adjustments not covered in main.css */

    /* All passives container scrolling */
    .all-passives-container {
        max-height: none; /* Allow natural height on mobile */
        overflow: visible;
    }

    /* Install prompt mobile */
    .install-prompt {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm, 1rem);
        padding: var(--spacing-md, 1.5rem);
        bottom: max(20px, env(safe-area-inset-bottom) + 20px);
        left: max(20px, env(safe-area-inset-left) + 20px);
        right: max(20px, env(safe-area-inset-right) + 20px);
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    /* Even more aggressive optimizations */
    :root {
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.75rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
    }
    
    .nav-orb {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .game-portal {
        height: 100px;
        padding: var(--spacing-sm, 1rem);
    }
    
    .dimension-title::before {
        left: -5px;
        right: -5px;
    }
}

/* CRITICAL FIX: Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 600px) {
    .game-dimension {
        padding-top: 60px;
        padding-bottom: 60px;
    }
    
    .dimension-header {
        margin-bottom: var(--spacing-md, 1.5rem);
    }
    
    .dimension-title {
        font-size: clamp(1.25rem, 4vh, 2rem);
    }
    
    /* Allow scrolling for content in landscape */
    .server-matrix,
    .settings-grid {
        max-height: none;
        overflow: visible;
    }
    
    .button-group {
        margin-top: var(--spacing-md, 1.5rem);
    }
    
    .nav-portal {
        top: 5px;
        right: 5px;
    }
    
    .dimension-indicator {
        bottom: 10px;
    }
}

/* CRITICAL FIX: Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    /* Increase all touch targets */
    .nav-orb,
    .indicator-dot,
    .quantum-button,
    .game-portal,
    .server-address,
    .install-btn,
    .close-btn {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* PERFORMANCE FIX: Remove hover effects on touch devices */
    .game-portal:hover,
    .server-node:hover,
    .quantum-button:hover,
    .nav-orb:hover,
    .passive-item:hover {
        transform: none !important;
        box-shadow: none !important;
    }
    
    /* CRITICAL FIX: Better touch feedback */
    .game-portal:active,
    .quantum-button:active,
    .nav-orb:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    /* Adjust server address for better mobile tapping */
    .server-address {
        padding: var(--spacing-sm, 1rem) var(--spacing-md, 1.5rem);
        font-size: var(--font-size-md, 1rem);
        border-radius: 8px;
    }
    
    /* PERFORMANCE FIX: Disable expensive effects on touch devices */
    .matrix-bg {
        opacity: 0.05;
    }
    
    .cursor {
        display: none !important;
    }
    
    /* Ensure scrollable areas work properly */
    .dimension,
    .game-dimension {
        -webkit-overflow-scrolling: touch;
    }
}

/* CRITICAL FIX: Safe area adjustments for notched devices */
@supports (padding: max(0px)) {
    .nav-portal {
        right: max(10px, env(safe-area-inset-right) + 10px);
        top: max(10px, env(safe-area-inset-top) + 10px);
    }
    
    .dimension-indicator {
        bottom: max(20px, env(safe-area-inset-bottom) + 20px);
        left: 50%;
        transform: translateX(-50%);
        padding: 0 max(10px, env(safe-area-inset-left) + 10px);
    }
    
    .install-prompt {
        bottom: max(20px, env(safe-area-inset-bottom) + 20px);
        left: max(20px, env(safe-area-inset-left) + 20px);
        right: max(20px, env(safe-area-inset-right) + 20px);
    }
    
    .game-dimension {
        padding-left: max(var(--spacing-sm, 1rem), env(safe-area-inset-left) + var(--spacing-sm, 1rem));
        padding-right: max(var(--spacing-sm, 1rem), env(safe-area-inset-right) + var(--spacing-sm, 1rem));
        padding-top: max(80px, env(safe-area-inset-top) + 80px);
        padding-bottom: max(80px, env(safe-area-inset-bottom) + 80px);
    }
}

/* PERFORMANCE FIX: Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .universe {
        transition: none;
    }
    
    .game-portal,
    .nav-orb,
    .quantum-button {
        transition: none;
    }
    
    .matrix-bg {
        display: none;
    }
    
    * {
        animation: none !important;
        transition: none !important;
    }
}

/* CRITICAL FIX: Print media to prevent issues */
@media print {
    body {
        overflow: visible !important;
        position: static !important;
        height: auto !important;
    }
    
    .universe {
        position: static !important;
        width: auto !important;
        height: auto !important;
        transform: none !important;
    }
    
    .dimension {
        width: auto !important;
        height: auto !important;
        overflow: visible !important;
        page-break-after: always;
    }
    
    .nav-portal,
    .dimension-indicator,
    .matrix-bg,
    .cursor,
    .loading-screen,
    .install-prompt,
    .offline-indicator {
        display: none !important;
    }
}