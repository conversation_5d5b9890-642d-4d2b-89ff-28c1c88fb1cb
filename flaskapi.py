from flask import Flask, jsonify
from flask_cors import CORS
import json
import os
from datetime import datetime
import threading
import time
from palworld_player_checker import PalworldPlayerChecker  # Import your cleaned script

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend requests

# Global variables to store cached data
cached_player_data = None
last_update = None
update_interval = 30  # seconds

def update_player_data():
    """Background function to update player data periodically"""
    global cached_player_data, last_update
    
    while True:
        try:
            print(f"🔄 Updating player data at {datetime.now()}")
            
            checker = PalworldPlayerChecker()
            results = checker.fetch_all_servers()
            
            # Format data for web consumption
            cached_player_data = {
                "last_updated": datetime.now().isoformat(),
                "total_players": checker.get_total_players(results),
                "servers": results,
                "status": "success"
            }
            
            last_update = datetime.now()
            
            # Optional: Save to file for backup
            with open('palworld_status.json', 'w') as f:
                json.dump(cached_player_data, f, indent=2)
                
            print(f"✅ Player data updated - Total players: {cached_player_data['total_players']}")
            
        except Exception as e:
            print(f"❌ Error updating player data: {e}")
            
            # Create error response
            cached_player_data = {
                "last_updated": datetime.now().isoformat(),
                "total_players": 0,
                "servers": {},
                "status": "error",
                "error": str(e)
            }
        
        time.sleep(update_interval)

@app.route('/api/palworld/players', methods=['GET'])
def get_palworld_players():
    """API endpoint to get current player data"""
    global cached_player_data, last_update
    
    # If no cached data exists, try to get fresh data
    if cached_player_data is None:
        try:
            checker = PalworldPlayerChecker()
            results = checker.fetch_all_servers()
            
            cached_player_data = {
                "last_updated": datetime.now().isoformat(),
                "total_players": checker.get_total_players(results),
                "servers": results,
                "status": "success"
            }
            last_update = datetime.now()
            
        except Exception as e:
            return jsonify({
                "last_updated": datetime.now().isoformat(),
                "total_players": 0,
                "servers": {},
                "status": "error",
                "error": str(e)
            }), 500
    
    # Add additional metadata
    response_data = cached_player_data.copy()
    if last_update:
        response_data["cache_age_seconds"] = (datetime.now() - last_update).total_seconds()
    
    return jsonify(response_data)

@app.route('/api/palworld/status', methods=['GET'])
def get_api_status():
    """Health check endpoint"""
    return jsonify({
        "status": "running",
        "version": "1.0.0",
        "last_update": last_update.isoformat() if last_update else None,
        "update_interval": update_interval,
        "cached_data_available": cached_player_data is not None
    })

@app.route('/api/palworld/players/refresh', methods=['POST'])
def refresh_player_data():
    """Force refresh player data"""
    try:
        checker = PalworldPlayerChecker()
        results = checker.fetch_all_servers()
        
        global cached_player_data, last_update
        cached_player_data = {
            "last_updated": datetime.now().isoformat(),
            "total_players": checker.get_total_players(results),
            "servers": results,
            "status": "success"
        }
        last_update = datetime.now()
        
        return jsonify({
            "status": "refreshed",
            "data": cached_player_data
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

if __name__ == '__main__':
    # Start background thread for periodic updates
    update_thread = threading.Thread(target=update_player_data, daemon=True)
    update_thread.start()
    
    print("🚀 Starting Palworld Player API Server...")
    print("📊 Player data will update every 30 seconds")
    print("🌐 API Endpoints:")
    print("   GET  /api/palworld/players - Get current player data")
    print("   GET  /api/palworld/status  - API health check")
    print("   POST /api/palworld/players/refresh - Force refresh data")
    
    # Run Flask app
    app.run(host='0.0.0.0', port=5000, debug=False)