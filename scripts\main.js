// scripts/main.js - Main initialization and coordination with robust error handling

// CRITICAL FIX: Global state with correct dimension count
window.xDreamState = {
    currentDimension: 0,
    totalDimensions: 6, // FIXED: Now correctly 6 dimensions (0-5: Home, Palworld, 7Days, Zomboid, Dune, Discord)
    autoAdvance: false,
    autoAdvanceInterval: null,
    isPageVisible: true,
    isInitialized: false,
    isNavigating: false, // ADDED: Navigation state management
    isMobile: false // ADDED: Mobile detection
};

// CACHE FIX: Force service worker to check for updates on page load
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then(registration => {
        registration.update();
    });
}

// CRITICAL FIX: Mobile detection and viewport initialization
function detectMobile() {
    const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const isSmallScreen = window.innerWidth <= 768;
    
    window.xDreamState.isMobile = isMobileDevice || hasTouch || isSmallScreen;
    
    if (window.xDreamState.isMobile) {
        document.body.classList.add('mobile-device');
        console.log('📱 Mobile device detected');
    }
}

// CRITICAL FIX: Scroll behavior initialization
function initializeScrollBehavior() {
    // CRITICAL FIX: Completely disable any scroll-based dimension navigation
    document.addEventListener('wheel', (e) => {
        // Check if we're in a scrollable content area
        const scrollableParent = e.target.closest(
            '.game-dimension, .dimension-content, .all-passives-container, ' +
            '.server-settings-container, .custom-passives-section, .zomboid-setup-section, ' +
            '.economy-section, .community-section, .dune-overview-section, .connection-section, ' +
            '.server-settings-section, .world-structure-section, .community-rules-section, ' +
            '.join-community-section, .discord-overview-section, .discord-features-section, ' +
            '.discord-commands-section, .discord-stats-section, .join-discord-section, ' +
            '.setup-guide-section, .quick-links-section'
        );

        if (scrollableParent) {
            // Allow normal scrolling in content areas
            const canScrollUp = scrollableParent.scrollTop > 0;
            const canScrollDown = scrollableParent.scrollTop < (scrollableParent.scrollHeight - scrollableParent.clientHeight - 1);

            // Only allow scroll if content can actually be scrolled
            if ((e.deltaY < 0 && canScrollUp) || (e.deltaY > 0 && canScrollDown)) {
                // Let the scroll happen naturally - don't prevent it
                return;
            } else {
                // Prevent scroll when at boundaries to avoid any navigation
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }

        // CRITICAL FIX: Prevent any scroll-based dimension navigation
        // If not in a scrollable area, prevent all scroll behavior
        e.preventDefault();
        e.stopPropagation();
        return false;
    }, { passive: false }); // Changed to non-passive to allow preventDefault

    console.log('🖱️ Scroll behavior initialized: Wheel scrolls content only, no dimension navigation');
}

// CRITICAL FIX: Robust initialization with error handling
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('🎮 xDREAM Gaming - Initializing...');
        
        // MOBILE FIX: Detect mobile first
        detectMobile();
        
        // CRITICAL FIX: Initialize scroll behavior first
        initializeScrollBehavior();
        
        // MOBILE FIX: Lock viewport on mobile first
        if (window.xDreamState.isMobile) {
            lockMobileViewport();
        }
        
        // Initialize modules with error handling
        initializeWithErrorHandling();
        
        // Set up critical event listeners
        setupCriticalListeners();
        
        // MOBILE FIX: Set initial URL state
        handleInitialNavigation();
        
        // Hide loading screen with delay
        setTimeout(() => {
            hideLoadingScreen();
        }, 1500);
        
        // Mark as initialized
        window.xDreamState.isInitialized = true;
        console.log('✅ xDREAM Gaming - Initialization complete');
        console.log('🎯 Navigation: Arrow keys, nav orbs, or swipe (mobile) - NO scroll wheel navigation');
        
    } catch (error) {
        console.error('❌ Critical initialization error:', error);
        handleInitializationError(error);
    }
});

// MOBILE FIX: Handle initial navigation from URL parameters
function handleInitialNavigation() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const dimension = urlParams.get('dimension');
        
        if (dimension !== null) {
            const dimNum = parseInt(dimension);
            if (dimNum >= 0 && dimNum < window.xDreamState.totalDimensions) {
                setTimeout(() => {
                    if (typeof navigateToDimension === 'function') {
                        navigateToDimension(dimNum);
                    }
                }, 100);
            }
        }
    } catch (error) {
        console.error('❌ Initial navigation error:', error);
    }
}

// PERFORMANCE FIX: Initialize modules with proper error boundaries
function initializeWithErrorHandling() {
    const modules = [
        { name: 'PWA', fn: initializePWA },
        { name: 'Navigation', fn: initializeNavigation },
        { name: 'Effects', fn: initializeEffects }
    ];
    
    modules.forEach(module => {
        try {
            if (typeof module.fn === 'function') {
                module.fn();
                console.log(`✅ ${module.name} module initialized`);
            } else {
                console.warn(`⚠️ ${module.name} module function not found`);
            }
        } catch (error) {
            console.error(`❌ ${module.name} module failed to initialize:`, error);
            // Continue with other modules even if one fails
        }
    });
}

// CRITICAL FIX: Handle initialization errors gracefully
function handleInitializationError(error) {
    // Show user-friendly error message
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        const loadingText = loadingScreen.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = 'INITIALIZATION ERROR - PLEASE REFRESH';
            loadingText.style.color = '#ff0000';
        }
    }
    
    // Still try to hide loading screen after a delay
    setTimeout(() => {
        hideLoadingScreen();
    }, 3000);
}

// CRITICAL FIX: Mobile viewport lock with better touch handling
function lockMobileViewport() {
    // MOBILE FIX: Prevent pull-to-refresh and overscroll with better detection
    let isScrollingAllowedContent = false;
    let touchStartY = 0;
    let lastTouchElement = null;
    
    document.addEventListener('touchstart', function(e) {
        touchStartY = e.touches[0].clientY;
        lastTouchElement = e.target;
        
        // MOBILE FIX: Check if touch started in scrollable content more accurately
        const scrollableElement = e.target.closest('.game-dimension, .dimension-content, .all-passives-container, .server-settings-container, .custom-passives-section, .zomboid-setup-section, .economy-section, .community-section, .dune-overview-section, .connection-section, .server-settings-section, .world-structure-section, .community-rules-section, .join-community-section, .discord-overview-section, .discord-features-section, .discord-commands-section, .discord-stats-section, .join-discord-section');
        
        if (scrollableElement) {
            // Check if the element actually has scrollable content
            const hasVerticalScroll = scrollableElement.scrollHeight > scrollableElement.clientHeight;
            isScrollingAllowedContent = hasVerticalScroll;
        } else {
            isScrollingAllowedContent = false;
        }
    }, { passive: true });
    
    document.addEventListener('touchmove', function(e) {
        // MOBILE FIX: Always allow vertical scrolling in game dimensions
        if (isScrollingAllowedContent) {
            return; // Allow normal scrolling
        }
        
        // MOBILE FIX: Check if we're in home dimension - prevent all scrolling there
        const homeElement = lastTouchElement?.closest('.home-dimension');
        if (homeElement) {
            e.preventDefault();
            return;
        }
        
        // MOBILE FIX: For other cases, allow vertical scrolling but prevent horizontal
        const currentY = e.touches[0].clientY;
        const deltaY = Math.abs(currentY - touchStartY);
        
        // If minimal vertical movement, might be horizontal swipe
        if (deltaY < 10) {
            e.preventDefault();
        }
    }, { passive: false });
    
    // MOBILE FIX: Prevent zoom on double tap with better timing
    let lastTouchTime = 0;
    let tapCount = 0;
    
    document.addEventListener('touchend', function(e) {
        const currentTime = Date.now();
        const timeDiff = currentTime - lastTouchTime;
        
        if (timeDiff < 300 && timeDiff > 0) {
            tapCount++;
            if (tapCount >= 2) {
                e.preventDefault();
                tapCount = 0;
            }
        } else {
            tapCount = 1;
        }
        
        lastTouchTime = currentTime;
        
        // Reset scrolling state
        isScrollingAllowedContent = false;
    }, { passive: false });
    
    // MOBILE FIX: Disable pinch zoom
    document.addEventListener('gesturestart', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    // MOBILE FIX: Prevent context menu on long press
    document.addEventListener('contextmenu', function(e) {
        if (window.xDreamState.isMobile) {
            e.preventDefault();
        }
    }, { passive: false });
    
    // MOBILE FIX: Handle orientation changes
    window.addEventListener('orientationchange', function() {
        // Small delay to ensure orientation change is complete
        setTimeout(() => {
            // Trigger a resize to recalculate layouts
            window.dispatchEvent(new Event('resize'));
            
            // Reset any scroll positions that might be incorrect
            if (window.xDreamState.currentDimension === 0) {
                // Home dimension should never scroll
                const homeElement = document.querySelector('.home-dimension');
                if (homeElement) {
                    homeElement.scrollTop = 0;
                }
            }
        }, 100);
    });
}

// CRITICAL FIX: Set up essential event listeners with error handling
function setupCriticalListeners() {
    // Visibility change handler with better browser support
    const visibilityChange = getVisibilityChangeEvent();
    if (visibilityChange) {
        document.addEventListener(visibilityChange, () => {
            window.xDreamState.isPageVisible = !document.hidden;
            
            // Pause/resume auto-advance based on visibility
            if (window.xDreamState.autoAdvance) {
                if (window.xDreamState.isPageVisible) {
                    console.log('🔄 Page visible - resuming auto-advance');
                } else {
                    console.log('⏸️ Page hidden - pausing auto-advance');
                }
            }
        });
    }
    
    // MOBILE FIX: Handle resize events for mobile layout adjustments
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            // Update mobile state if screen size changes
            const wasM = window.xDreamState.isMobile;
            detectMobile();
            
            if (wasM !== window.xDreamState.isMobile) {
                console.log('📱 Mobile state changed, reinitializing...');
                // Optionally reinitialize some components
            }
            
            // Ensure proper dimension positioning after resize
            const universe = document.getElementById('universe');
            if (universe && window.xDreamState.currentDimension !== undefined) {
                universe.style.transform = `translate3d(-${window.xDreamState.currentDimension * 100}vw, 0, 0)`;
            }
        }, 250);
    });
    
    // Error handling for unhandled errors
    window.addEventListener('error', (e) => {
        console.error('💥 Unhandled error:', e.error);
        // Don't let errors break the entire site
    });
    
    // Error handling for unhandled promise rejections
    window.addEventListener('unhandledrejection', (e) => {
        console.error('💥 Unhandled promise rejection:', e.reason);
        e.preventDefault(); // Prevent default browser error handling
    });
    
    // PERFORMANCE FIX: Handle page unload cleanup
    window.addEventListener('beforeunload', () => {
        if (typeof cleanupEffects === 'function') {
            cleanupEffects();
        }
        
        // Clear auto-advance
        if (window.xDreamState.autoAdvanceInterval) {
            clearInterval(window.xDreamState.autoAdvanceInterval);
        }
    });
    
    // MOBILE FIX: Handle page focus/blur for better mobile experience
    window.addEventListener('focus', () => {
        window.xDreamState.isPageVisible = true;
    });
    
    window.addEventListener('blur', () => {
        window.xDreamState.isPageVisible = false;
    });
}

// COMPATIBILITY FIX: Get visibility change event name for different browsers
function getVisibilityChangeEvent() {
    if (typeof document.hidden !== "undefined") {
        return "visibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
        return "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
        return "webkitvisibilitychange";
    }
    return null;
}

// PERFORMANCE FIX: Smooth loading screen hide with error handling
function hideLoadingScreen() {
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
        loadingScreen.classList.add('hidden');
        
        // MEMORY FIX: Remove loading screen from DOM after transition
        setTimeout(() => {
            if (loadingScreen.parentNode) {
                loadingScreen.parentNode.removeChild(loadingScreen);
            }
        }, 500);
    }
}

// Window load event for deferred operations with error handling
window.addEventListener('load', () => {
    try {
        console.log('🌟 Page fully loaded - starting deferred operations');
        
        // MOBILE FIX: Only create particles on non-mobile devices for performance
        if (!window.xDreamState.isMobile) {
            // Create zombie particles after everything is loaded
            if (typeof createZombieParticles === 'function') {
                createZombieParticles();
            }
            
            // Create sand particles for Dune dimension
            if (typeof createSandParticles === 'function') {
                createSandParticles();
            }
            
            // Start ambient effects
            if (typeof startAmbientEffects === 'function') {
                startAmbientEffects();
            }
        }
        
        // Initialize Palworld player count if available
        setTimeout(() => {
            if (typeof initializePalworldPlayerCount === 'function') {
                initializePalworldPlayerCount();
            }
        }, 2000);
        
        // MOBILE FIX: Ensure proper initial state after load
        setTimeout(() => {
            const universe = document.getElementById('universe');
            if (universe) {
                // Ensure we're at the correct dimension
                universe.style.transform = `translate3d(-${window.xDreamState.currentDimension * 100}vw, 0, 0)`;
            }
        }, 100);
        
        // Console welcome message with updated navigation info
        console.log('🎮 xDREAM Gaming Community - Revolutionary Interface Loaded');
        console.log('🔥 Navigation: Arrow keys or click nav orbs (top right)');
        console.log('⚡ Features: Space for auto-advance, try the Konami code!');
        console.log('📱 Mobile: Swipe left/right to navigate dimensions');
        console.log('🖱️ IMPORTANT: Scroll wheel scrolls content ONLY - no dimension navigation');
        console.log('👥 Palworld: Live player counts updated every 30 seconds');
        console.log('🏜️ Dune: Awakening - The spice must flow!');
        console.log('💬 Discord: Join our community hub!');
        
    } catch (error) {
        console.error('❌ Deferred operations error:', error);
        // Don't let this break the site
    }
});

// PERFORMANCE FIX: Debounce utility for better performance
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// PERFORMANCE FIX: Throttle utility for high-frequency events
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// MOBILE FIX: Add global function to force navigation (useful for debugging)
window.xDreamNavigate = function(dimension) {
    if (typeof navigateToDimension === 'function') {
        navigateToDimension(dimension);
    } else {
        console.error('Navigation function not available');
    }
};

// DIAGNOSTICS: Add global function for debugging
window.xDreamDiagnostics = function() {
    console.log('🔍 xDREAM Diagnostics:');
    console.log('State:', window.xDreamState);
    console.log('Current dimension:', window.xDreamState.currentDimension);
    console.log('Total dimensions:', window.xDreamState.totalDimensions);
    console.log('Page visible:', window.xDreamState.isPageVisible);
    console.log('Initialized:', window.xDreamState.isInitialized);
    console.log('Mobile device:', window.xDreamState.isMobile);
    console.log('Universe element:', document.getElementById('universe'));
    console.log('Matrix canvas:', document.getElementById('matrix'));
    console.log('Navigation function available:', typeof navigateToDimension !== 'undefined');
    console.log('Scroll navigation: DISABLED (content scrolling only)');
    
    // Check current universe position
    const universe = document.getElementById('universe');
    if (universe) {
        console.log('Universe transform:', universe.style.transform);
    }
    
    // Check nav orbs
    const navOrbs = document.querySelectorAll('.nav-orb');
    console.log('Nav orbs found:', navOrbs.length);
    navOrbs.forEach((orb, index) => {
        console.log(`Nav orb ${index}:`, orb.dataset.dimension, orb.classList.contains('active') ? '(active)' : '');
    });
};

// MOBILE FIX: Add mobile-specific debugging
window.xDreamMobileDiag = function() {
    console.log('📱 Mobile Diagnostics:');
    console.log('User Agent:', navigator.userAgent);
    console.log('Touch support:', 'ontouchstart' in window);
    console.log('Max touch points:', navigator.maxTouchPoints);
    console.log('Screen size:', window.innerWidth + 'x' + window.innerHeight);
    console.log('Device pixel ratio:', window.devicePixelRatio);
    console.log('Orientation:', screen.orientation?.type || 'unknown');
    console.log('Mobile detected:', window.xDreamState.isMobile);
    
    // Check viewport
    const viewport = document.querySelector('meta[name="viewport"]');
    console.log('Viewport meta:', viewport?.content || 'not found');
    
    // Check body classes
    console.log('Body classes:', document.body.className);
    
    // Check for mobile-specific styles
    const mobileStylesApplied = window.getComputedStyle(document.body).position;
    console.log('Body position (should be fixed on mobile):', mobileStylesApplied);
};