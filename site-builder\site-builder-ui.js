// Complete Site Builder UI - All Features Implemented with Fixes
class SiteBuilderUI {
    constructor() {
        this.currentPageId = null;
        this.previewMode = false;
        this.isDragging = false;
        this.draggedElement = null;
        this.draggedIndex = null;
        this.autoSaveTimer = null;
        this.unsavedChanges = false;
        this.previewWindow = null;
        this.contentChangeListeners = new Map();
        
        this.init();
    }

    async init() {
        await this.waitForDependencies();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.setupDragAndDrop();
        this.renderPagesList();
        this.updateEditorButtons();
        this.updateSiteStats();
        this.setupAutoSaveIndicator();
        
        console.log('🎨 Complete Site Builder UI initialized with all features');
    }

    async waitForDependencies() {
        const maxWait = 5000;
        const startTime = Date.now();
        
        while (!window.siteBuilderCore) {
            if (Date.now() - startTime > maxWait) {
                throw new Error('Site Builder Core failed to load');
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // Enhanced Pages List Management
    renderPagesList() {
        const pagesList = document.getElementById('pagesList');
        if (!pagesList) return;

        const pages = window.siteBuilderCore.getAllPages();
        pagesList.innerHTML = '';

        if (pages.length === 0) {
            pagesList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <p>No pages yet. Create your first page!</p>
                    <button class="quantum-button" onclick="siteBuilderUI.showAddPageModal()">
                        ➕ Create Page
                    </button>
                </div>
            `;
            return;
        }

        // Create sortable pages list
        const pagesContainer = document.createElement('div');
        pagesContainer.className = 'pages-sortable-container';
        pagesContainer.id = 'pagesSortableContainer';

        pages.forEach(page => {
            const pageItem = this.createPageItem(page);
            pagesContainer.appendChild(pageItem);
        });

        pagesList.appendChild(pagesContainer);
        this.setupPagesSorting();

        console.log(`📋 Rendered ${pages.length} pages in sidebar`);
    }

    createPageItem(page) {
        const item = document.createElement('div');
        item.className = 'page-item';
        item.dataset.pageId = page.id;
        item.dataset.order = page.order;
        item.draggable = !page.isDefault;
        
        if (page.id === this.currentPageId) {
            item.classList.add('active');
        }
        
        if (page.isDefault) {
            item.classList.add('default-page');
        }

        const statusIndicator = this.unsavedChanges && page.id === this.currentPageId ? 
            '<span class="unsaved-indicator" title="Unsaved changes">●</span>' : '';

        item.innerHTML = `
            <div class="drag-handle-pages" style="${page.isDefault ? 'visibility: hidden;' : ''}">⋮⋮</div>
            <div class="page-icon" style="color: ${page.accentColor}">${page.icon}</div>
            <div class="page-info">
                <div class="page-name">
                    ${this.escapeHtml(page.title)}
                    ${statusIndicator}
                </div>
                <div class="page-subtitle">${this.escapeHtml(page.subtitle) || 'No subtitle'}</div>
                <div class="page-meta">
                    <span class="page-order">Position: ${page.order + 1}</span>
                    ${page.isDefault ? '<span class="default-badge">Default</span>' : ''}
                </div>
            </div>
            <div class="page-actions">
                <button class="action-btn preview-btn" title="Preview page" onclick="siteBuilderUI.previewPage('${page.id}', event)">
                    👁️
                </button>
                <button class="action-btn duplicate-btn" title="Duplicate page" onclick="siteBuilderUI.duplicatePage('${page.id}', event)">
                    📋
                </button>
                ${!page.isDefault ? `<button class="action-btn delete-btn" title="Delete page" onclick="siteBuilderUI.confirmDeletePage('${page.id}', event)">🗑️</button>` : ''}
            </div>
        `;

        item.addEventListener('click', (e) => {
            if (!e.target.closest('.page-actions') && !e.target.closest('.drag-handle-pages')) {
                this.selectPage(page.id);
            }
        });

        return item;
    }

    setupPagesSorting() {
        const container = document.getElementById('pagesSortableContainer');
        if (!container) return;

        let draggedPageElement = null;
        let draggedPageId = null;

        container.addEventListener('dragstart', (e) => {
            const pageItem = e.target.closest('.page-item');
            if (!pageItem || pageItem.classList.contains('default-page')) return;
            
            draggedPageElement = pageItem;
            draggedPageId = pageItem.dataset.pageId;
            pageItem.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
        });

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            if (!draggedPageElement) return;

            const afterElement = this.getDragAfterElement(container, e.clientY, '.page-item');
            if (afterElement == null) {
                container.appendChild(draggedPageElement);
            } else {
                container.insertBefore(draggedPageElement, afterElement);
            }
        });

        container.addEventListener('dragend', (e) => {
            if (!draggedPageElement) return;
            
            draggedPageElement.classList.remove('dragging');
            
            // Update page order
            const pageItems = container.querySelectorAll('.page-item');
            const newOrder = Array.from(pageItems).findIndex(item => 
                item.dataset.pageId === draggedPageId
            );
            
            if (newOrder !== -1) {
                window.siteBuilderCore.reorderPage(draggedPageId, newOrder);
                this.renderPagesList();
            }
            
            draggedPageElement = null;
            draggedPageId = null;
        });
    }

    selectPage(pageId) {
        if (this.currentPageId && this.unsavedChanges) {
            if (!confirm('You have unsaved changes. Do you want to save them before switching pages?')) {
                return;
            }
            this.saveCurrentPage();
        }

        this.currentPageId = pageId;
        this.unsavedChanges = false;
        this.clearContentChangeListeners();
        this.renderPagesList();
        this.renderPageEditor(pageId);
        this.updateEditorButtons();
        this.updatePreview();
        
        // Update URL hash for bookmarking
        window.location.hash = `page-${pageId}`;
        
        console.log(`📄 Selected page: ${pageId}`);
    }

    // Complete Page Editor Implementation
    renderPageEditor(pageId) {
        const page = window.siteBuilderCore.getPage(pageId);
        if (!page) {
            this.clearEditor();
            return;
        }

        const editorContent = document.getElementById('editorContent');
        const editorTitle = document.getElementById('editorTitle');
        
        if (editorTitle) {
            editorTitle.innerHTML = `
                Editing: <span style="color: ${page.accentColor}">${this.escapeHtml(page.title)}</span>
                <span class="page-status">${page.isDefault ? ' (Default Page)' : ''}</span>
            `;
        }

        if (editorContent) {
            editorContent.innerHTML = this.createAdvancedPageEditor(page);
            this.setupFormEventListeners(page);
            this.setupContentBlockSorting();
            this.updateColorPreview();
            this.updateIconPreview();
        }
    }

    createAdvancedPageEditor(page) {
        return `
            <div class="form-container">
                <!-- Quick Actions Bar -->
                <div class="quick-actions-bar">
                    <button type="button" class="quick-action-btn" onclick="siteBuilderUI.togglePreview()" title="Toggle Preview">
                        👁️ ${this.previewMode ? 'Hide' : 'Show'} Preview
                    </button>
                    <button type="button" class="quick-action-btn ${this.unsavedChanges ? 'has-changes' : ''}" onclick="siteBuilderUI.saveCurrentPage()" title="Save Page">
                        💾 Save${this.unsavedChanges ? ' Changes' : ''}
                    </button>
                    <button type="button" class="quick-action-btn" onclick="siteBuilderUI.previewInNewWindow()" title="Preview in New Window">
                        🚀 Open Preview
                    </button>
                    <button type="button" class="quick-action-btn" onclick="siteBuilderUI.duplicatePage('${page.id}')" title="Duplicate Page">
                        📋 Duplicate
                    </button>
                    ${!page.isDefault ? `<button type="button" class="quick-action-btn danger" onclick="siteBuilderUI.confirmDeletePage('${page.id}')" title="Delete Page">🗑️ Delete</button>` : ''}
                </div>

                <!-- Basic Information -->
                <div class="form-section">
                    <h3 class="form-section-title">📝 Basic Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editPageTitle">Page Title *</label>
                            <input type="text" id="editPageTitle" value="${this.escapeHtml(page.title)}" required maxlength="50" placeholder="Enter page title">
                            <div class="field-help">This appears in navigation and page headers</div>
                        </div>
                        <div class="form-group">
                            <label for="editPageSubtitle">Page Subtitle</label>
                            <input type="text" id="editPageSubtitle" value="${this.escapeHtml(page.subtitle || '')}" maxlength="100" placeholder="Optional subtitle">
                            <div class="field-help">Appears below the main title</div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="editPageIcon">Navigation Icon *</label>
                            <div class="icon-input-group">
                                <input type="text" id="editPageIcon" value="${page.icon}" maxlength="2" placeholder="🎮" required>
                                <div class="icon-preview" id="iconPreview" style="color: ${page.accentColor}">${page.icon}</div>
                                <button type="button" class="icon-picker-btn" onclick="siteBuilderUI.showIconPicker()">
                                    📋 Choose
                                </button>
                            </div>
                            <div class="field-help">Emoji shown in navigation (max 2 characters)</div>
                        </div>
                        <div class="form-group">
                            <label for="editAccentColor">Accent Color</label>
                            <div class="color-input-group">
                                <input type="color" id="editAccentColor" value="${page.accentColor}">
                                <input type="text" id="editAccentColorText" value="${page.accentColor}" pattern="^#[0-9A-Fa-f]{6}$" placeholder="#00ffff">
                                <button type="button" class="color-preset-btn" onclick="siteBuilderUI.showColorPresets()">
                                    🎨 Presets
                                </button>
                            </div>
                            <div class="field-help">Primary color for this page's theme</div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="form-section collapsible-section ${page._seoCollapsed ? 'collapsed' : ''}">
                    <h3 class="form-section-title" onclick="siteBuilderUI.toggleSection(this, 'seo')">
                        🔍 SEO Settings <span class="section-toggle">${page._seoCollapsed ? '▶' : '▼'}</span>
                    </h3>
                    <div class="section-content" ${page._seoCollapsed ? 'style="display: none;"' : ''}>
                        <div class="form-group">
                            <label for="editPageDescription">Meta Description</label>
                            <textarea id="editPageDescription" maxlength="160" placeholder="Brief description for search engines...">${this.escapeHtml(page.seo?.description || '')}</textarea>
                            <div class="character-count">
                                <span id="descCharCount">${(page.seo?.description || '').length}</span>/160 characters
                            </div>
                            <div class="field-help">Description shown in search results</div>
                        </div>
                        <div class="form-group">
                            <label for="editPageKeywords">Keywords</label>
                            <input type="text" id="editPageKeywords" value="${this.escapeHtml(page.seo?.keywords || '')}" placeholder="gaming, community, servers">
                            <div class="field-help">Comma-separated keywords for search engines</div>
                        </div>
                    </div>
                </div>

                <!-- Background Effects -->
                <div class="form-section collapsible-section ${page._effectsCollapsed ? 'collapsed' : ''}">
                    <h3 class="form-section-title" onclick="siteBuilderUI.toggleSection(this, 'effects')">
                        ✨ Background Effects <span class="section-toggle">${page._effectsCollapsed ? '▶' : '▼'}</span>
                    </h3>
                    <div class="section-content" ${page._effectsCollapsed ? 'style="display: none;"' : ''}>
                        <div class="effects-grid">
                            <label class="effect-option">
                                <input type="checkbox" id="editParticles" ${page.backgroundEffects?.particles ? 'checked' : ''}>
                                <div class="effect-card">
                                    <div class="effect-icon">🌟</div>
                                    <div class="effect-name">Particle Effects</div>
                                    <div class="effect-desc">Animated floating particles</div>
                                </div>
                            </label>
                            <label class="effect-option">
                                <input type="checkbox" id="editMatrix" ${page.backgroundEffects?.matrix !== false ? 'checked' : ''}>
                                <div class="effect-card">
                                    <div class="effect-icon">🌌</div>
                                    <div class="effect-name">Matrix Background</div>
                                    <div class="effect-desc">Falling matrix characters</div>
                                </div>
                            </label>
                            <label class="effect-option">
                                <input type="checkbox" id="editGlitch" ${page.backgroundEffects?.glitch ? 'checked' : ''}>
                                <div class="effect-card">
                                    <div class="effect-icon">⚡</div>
                                    <div class="effect-name">Glitch Effects</div>
                                    <div class="effect-desc">Digital distortion effects</div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Content Editor -->
                <div class="form-section">
                    <h3 class="form-section-title">📄 Content Blocks</h3>
                    <div class="content-editor-toolbar">
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn" onclick="siteBuilderUI.addContentBlock('text')" title="Add Text Block">
                                📝 Text
                            </button>
                            <button type="button" class="toolbar-btn" onclick="siteBuilderUI.addContentBlock('server-node')" title="Add Server Info">
                                🖥️ Server
                            </button>
                            <button type="button" class="toolbar-btn" onclick="siteBuilderUI.addContentBlock('button')" title="Add Button">
                                🔘 Button
                            </button>
                            <button type="button" class="toolbar-btn" onclick="siteBuilderUI.addContentBlock('image')" title="Add Image">
                                🖼️ Image
                            </button>
                            <button type="button" class="toolbar-btn" onclick="siteBuilderUI.addContentBlock('custom')" title="Add Custom HTML">
                                🛠️ Custom
                            </button>
                            <button type="button" class="toolbar-btn" onclick="siteBuilderUI.showContentTemplates()" title="Use Template">
                                📋 Templates
                            </button>
                        </div>
                        <div class="toolbar-group">
                            <button type="button" class="toolbar-btn secondary" onclick="siteBuilderUI.clearAllContent()" title="Clear All Content">
                                🗑️ Clear All
                            </button>
                            <button type="button" class="toolbar-btn secondary" onclick="siteBuilderUI.expandAllBlocks()" title="Expand All Blocks">
                                📖 Expand All
                            </button>
                            <button type="button" class="toolbar-btn secondary" onclick="siteBuilderUI.collapseAllBlocks()" title="Collapse All Blocks">
                                📕 Collapse All
                            </button>
                        </div>
                    </div>
                    <div class="content-blocks-container">
                        <div class="content-blocks" id="contentBlocks">
                            ${this.renderContentBlocks(page.content || [])}
                        </div>
                        <div class="add-content-hint">
                            ${(page.content || []).length === 0 ? 
                                '<div class="empty-content"><div class="empty-icon">📝</div><p>No content blocks yet. Use the toolbar above to add content.</p></div>' : 
                                '<div class="drop-zone" ondrop="siteBuilderUI.handleContentDrop(event)" ondragover="siteBuilderUI.handleContentDragOver(event)">Drop content blocks here or use the toolbar above</div>'
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Enhanced Content Block Management
    renderContentBlocks(content) {
        if (!content || content.length === 0) return '';
        
        return content.map((block, index) => this.createAdvancedContentBlock(block, index)).join('');
    }

    createAdvancedContentBlock(block, index) {
        const blockId = `block-${index}`;
        const isCollapsed = block._collapsed || false;
        
        return `
            <div class="content-block ${isCollapsed ? 'collapsed' : ''}" data-index="${index}" data-type="${block.type}" data-block-id="${blockId}" draggable="true" ondragstart="siteBuilderUI.handleBlockDragStart(event, ${index})">
                <div class="content-block-header">
                    <div class="block-header-left">
                        <span class="drag-handle" title="Drag to reorder">⋮⋮</span>
                        <span class="block-type-icon">${this.getBlockTypeIcon(block.type)}</span>
                        <span class="block-type-name">${this.getBlockTypeName(block.type)}</span>
                        <span class="block-preview">${this.getBlockPreview(block)}</span>
                    </div>
                    <div class="block-header-right">
                        <button type="button" class="block-action-btn" onclick="siteBuilderUI.toggleBlockCollapse(${index})" title="Toggle collapse">
                            ${isCollapsed ? '📖' : '📕'}
                        </button>
                        <button type="button" class="block-action-btn" onclick="siteBuilderUI.moveBlockUp(${index})" title="Move up" ${index === 0 ? 'disabled' : ''}>
                            ⬆️
                        </button>
                        <button type="button" class="block-action-btn" onclick="siteBuilderUI.moveBlockDown(${index})" title="Move down" ${index === (content.length - 1) ? 'disabled' : ''}>
                            ⬇️
                        </button>
                        <button type="button" class="block-action-btn" onclick="siteBuilderUI.duplicateContentBlock(${index})" title="Duplicate block">
                            📋
                        </button>
                        <button type="button" class="block-action-btn danger" onclick="siteBuilderUI.removeContentBlock(${index})" title="Remove block">
                            🗑️
                        </button>
                    </div>
                </div>
                <div class="content-block-body ${isCollapsed ? 'hidden' : ''}">
                    ${this.createBlockContent(block, index)}
                </div>
            </div>
        `;
    }

    setupContentBlockSorting() {
        const container = document.getElementById('contentBlocks');
        if (!container) return;

        container.addEventListener('dragover', (e) => {
            e.preventDefault();
            if (!this.draggedElement) return;

            const afterElement = this.getDragAfterElement(container, e.clientY, '.content-block');
            if (afterElement == null) {
                container.appendChild(this.draggedElement);
            } else {
                container.insertBefore(this.draggedElement, afterElement);
            }
        });

        container.addEventListener('drop', (e) => {
            e.preventDefault();
            if (!this.draggedElement || this.draggedIndex === null) return;

            const blocks = Array.from(container.querySelectorAll('.content-block'));
            const newIndex = blocks.indexOf(this.draggedElement);

            if (newIndex !== -1 && newIndex !== this.draggedIndex) {
                this.reorderContentBlock(this.draggedIndex, newIndex);
            }

            this.draggedElement = null;
            this.draggedIndex = null;
        });
    }

    handleBlockDragStart(event, index) {
        this.draggedElement = event.target.closest('.content-block');
        this.draggedIndex = index;
        event.dataTransfer.effectAllowed = 'move';
        this.draggedElement.classList.add('dragging');
    }

    handleContentDragOver(event) {
        event.preventDefault();
    }

    handleContentDrop(event) {
        event.preventDefault();
        if (this.draggedElement) {
            this.draggedElement.classList.remove('dragging');
        }
    }

    getDragAfterElement(container, y, selector) {
        const draggableElements = [...container.querySelectorAll(`${selector}:not(.dragging)`)];
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    reorderContentBlock(oldIndex, newIndex) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content) return;

        const [removed] = page.content.splice(oldIndex, 1);
        page.content.splice(newIndex, 0, removed);

        this.markUnsavedChanges();
        this.renderPageEditor(this.currentPageId);
        this.updatePreview();
        
        console.log(`🔄 Reordered content block from ${oldIndex} to ${newIndex}`);
    }

    // Enhanced Content Block Operations
    addContentBlock(type) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page) return;

        const newBlock = this.createNewContentBlock(type);
        page.content = page.content || [];
        page.content.push(newBlock);

        this.markUnsavedChanges();
        this.renderPageEditor(this.currentPageId);
        this.updatePreview();
        
        // Scroll to and highlight new block
        setTimeout(() => {
            const newBlockElement = document.querySelector(`[data-index="${page.content.length - 1}"]`);
            if (newBlockElement) {
                newBlockElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                newBlockElement.classList.add('highlight-new');
                setTimeout(() => newBlockElement.classList.remove('highlight-new'), 2000);
            }
        }, 100);
        
        console.log(`➕ Added ${type} content block`);
    }

    moveBlockUp(index) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content || index === 0) return;

        [page.content[index - 1], page.content[index]] = [page.content[index], page.content[index - 1]];
        
        this.markUnsavedChanges();
        this.renderPageEditor(this.currentPageId);
        this.updatePreview();
        
        // Scroll to moved block
        setTimeout(() => {
            const movedBlock = document.querySelector(`[data-index="${index - 1}"]`);
            if (movedBlock) {
                movedBlock.scrollIntoView({ behavior: 'smooth', block: 'center' });
                movedBlock.classList.add('highlight-moved');
                setTimeout(() => movedBlock.classList.remove('highlight-moved'), 1000);
            }
        }, 100);
        
        console.log(`⬆️ Moved content block up from index ${index}`);
    }

    moveBlockDown(index) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content || index >= page.content.length - 1) return;

        [page.content[index], page.content[index + 1]] = [page.content[index + 1], page.content[index]];
        
        this.markUnsavedChanges();
        this.renderPageEditor(this.currentPageId);
        this.updatePreview();
        
        // Scroll to moved block
        setTimeout(() => {
            const movedBlock = document.querySelector(`[data-index="${index + 1}"]`);
            if (movedBlock) {
                movedBlock.scrollIntoView({ behavior: 'smooth', block: 'center' });
                movedBlock.classList.add('highlight-moved');
                setTimeout(() => movedBlock.classList.remove('highlight-moved'), 1000);
            }
        }, 100);
        
        console.log(`⬇️ Moved content block down from index ${index}`);
    }

    duplicateContentBlock(index) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content || !page.content[index]) return;

        const originalBlock = page.content[index];
        const duplicatedBlock = JSON.parse(JSON.stringify(originalBlock));
        duplicatedBlock._collapsed = false; // Expand duplicated block
        
        page.content.splice(index + 1, 0, duplicatedBlock);
        this.markUnsavedChanges();
        this.renderPageEditor(this.currentPageId);
        this.updatePreview();
        
        // Scroll to duplicated block
        setTimeout(() => {
            const duplicatedElement = document.querySelector(`[data-index="${index + 1}"]`);
            if (duplicatedElement) {
                duplicatedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                duplicatedElement.classList.add('highlight-new');
                setTimeout(() => duplicatedElement.classList.remove('highlight-new'), 2000);
            }
        }, 100);
        
        console.log(`📋 Duplicated content block at index ${index}`);
    }

    removeContentBlock(index) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content) return;

        if (confirm('Are you sure you want to remove this content block?')) {
            page.content.splice(index, 1);
            this.markUnsavedChanges();
            this.renderPageEditor(this.currentPageId);
            this.updatePreview();
            console.log(`🗑️ Removed content block at index ${index}`);
        }
    }

    clearAllContent() {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page) return;

        if (confirm('Are you sure you want to clear all content blocks? This cannot be undone.')) {
            page.content = [];
            this.markUnsavedChanges();
            this.renderPageEditor(this.currentPageId);
            this.updatePreview();
            console.log('🗑️ Cleared all content blocks');
        }
    }

    expandAllBlocks() {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content) return;

        page.content.forEach(block => block._collapsed = false);
        this.renderPageEditor(this.currentPageId);
        console.log('📖 Expanded all content blocks');
    }

    collapseAllBlocks() {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content) return;

        page.content.forEach(block => block._collapsed = true);
        this.renderPageEditor(this.currentPageId);
        console.log('📕 Collapsed all content blocks');
    }

    toggleBlockCollapse(index) {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page || !page.content || !page.content[index]) return;

        page.content[index]._collapsed = !page.content[index]._collapsed;
        this.renderPageEditor(this.currentPageId);
    }

    // Advanced Preview System
    togglePreview() {
        const previewPanel = document.getElementById('previewPanel');
        if (!previewPanel) return;

        this.previewMode = !this.previewMode;
        
        if (this.previewMode) {
            previewPanel.classList.add('active');
            this.updatePreview();
        } else {
            previewPanel.classList.remove('active');
        }

        // Update preview button text
        const previewBtn = document.getElementById('previewBtn');
        if (previewBtn) {
            previewBtn.textContent = this.previewMode ? '🔍 HIDE PREVIEW' : '👁️ PREVIEW';
        }

        console.log(`👁️ Preview mode: ${this.previewMode ? 'ON' : 'OFF'}`);
    }

    updatePreview() {
        if (!this.previewMode || !this.currentPageId) return;

        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page) return;

        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        const previewHTML = this.generatePagePreview(page);
        previewContent.innerHTML = previewHTML;
        
        // Apply theme colors
        const previewElement = previewContent.querySelector('.preview-dimension');
        if (previewElement) {
            previewElement.style.setProperty('--accent-color', page.accentColor);
        }

        // Initialize any preview interactions
        this.initializePreviewInteractions();
    }

    initializePreviewInteractions() {
        // Add copy functionality to server addresses in preview
        const serverAddresses = document.querySelectorAll('.preview-content .server-address');
        serverAddresses.forEach(address => {
            address.addEventListener('click', (e) => {
                e.preventDefault();
                const text = address.textContent;
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        this.showCopyNotification(text);
                    });
                }
            });
        });
    }

    showCopyNotification(text) {
        const notification = document.createElement('div');
        notification.className = 'copy-notification-preview';
        notification.textContent = `Copied: ${text}`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 2000);
    }

    generatePagePreview(page) {
        let contentHTML = '';
        
        if (page.content && page.content.length > 0) {
            contentHTML = page.content.map(block => {
                switch (block.type) {
                    case 'text':
                    case 'custom':
                        return `<div class="preview-text-block">${block.content || ''}</div>`;
                    case 'server-node':
                        return `
                            <div class="preview-server-node">
                                <div class="server-name">${this.escapeHtml(block.serverName || 'Server Name')}</div>
                                <div class="server-address">${this.escapeHtml(block.serverAddress || 'server.address.com:port')}</div>
                                <div class="server-info">${this.escapeHtml(block.serverInfo || 'Server Info')}</div>
                                ${block.showPlayerCount ? '<div class="player-count preview-player-count"><span class="player-icon">👥</span><span class="player-text">24/32 Players</span></div>' : ''}
                            </div>
                        `;
                    case 'button':
                        return `
                            <div class="preview-button-container">
                                <button class="${block.style || 'quantum-button'}" disabled>
                                    ${this.escapeHtml(block.text || 'Button')}
                                </button>
                            </div>
                        `;
                    case 'image':
                        return block.src ? `
                            <div class="preview-image-container">
                                <img src="${block.src}" alt="${this.escapeHtml(block.alt || '')}" style="max-width: 100%; height: auto;">
                                ${block.caption ? `<div class="image-caption">${this.escapeHtml(block.caption)}</div>` : ''}
                            </div>
                        ` : '<div class="preview-placeholder">Image: No source provided</div>';
                    default:
                        return '';
                }
            }).join('');
        }

        // Add background effects indicators
        const effectsIndicators = [];
        if (page.backgroundEffects?.particles) effectsIndicators.push('🌟 Particles');
        if (page.backgroundEffects?.matrix !== false) effectsIndicators.push('🌌 Matrix');
        if (page.backgroundEffects?.glitch) effectsIndicators.push('⚡ Glitch');

        return `
            <div class="preview-dimension" style="--accent-color: ${page.accentColor}">
                ${effectsIndicators.length > 0 ? `
                    <div class="preview-effects-indicator">
                        Effects: ${effectsIndicators.join(' • ')}
                    </div>
                ` : ''}
                <div class="preview-header">
                    <h2 class="preview-title" style="color: ${page.accentColor}">${this.escapeHtml(page.title)}</h2>
                    ${page.subtitle ? `<div class="preview-subtitle">${this.escapeHtml(page.subtitle)}</div>` : ''}
                </div>
                <div class="preview-content">
                    ${contentHTML || '<div class="preview-placeholder">No content blocks added yet</div>'}
                </div>
            </div>
        `;
    }

    previewInNewWindow() {
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page) return;

        const previewHTML = this.generateFullPagePreview(page);
        
        if (this.previewWindow && !this.previewWindow.closed) {
            this.previewWindow.document.write(previewHTML);
            this.previewWindow.document.close();
            this.previewWindow.focus();
        } else {
            this.previewWindow = window.open('', 'preview', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            this.previewWindow.document.write(previewHTML);
            this.previewWindow.document.close();
        }
        
        console.log('🚀 Opened preview in new window');
    }

    generateFullPagePreview(page) {
        const previewContent = this.generatePagePreview(page);
        
        return `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Preview: ${this.escapeHtml(page.title)}</title>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    
                    body {
                        font-family: 'Courier New', monospace;
                        background: #0a0a0a;
                        color: #ffffff;
                        min-height: 100vh;
                        padding: 20px;
                        position: relative;
                    }
                    
                    .preview-dimension {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 20px;
                    }
                    
                    .preview-effects-indicator {
                        position: fixed;
                        top: 10px;
                        left: 10px;
                        background: rgba(0, 0, 0, 0.8);
                        padding: 10px 15px;
                        border-radius: 5px;
                        border: 1px solid var(--accent-color, #00ffff);
                        font-size: 14px;
                        z-index: 1000;
                    }
                    
                    .preview-header {
                        text-align: center;
                        margin-bottom: 3rem;
                        padding: 2rem;
                        background: rgba(0, 0, 0, 0.5);
                        border-radius: 15px;
                        border: 2px solid var(--accent-color, #00ffff);
                    }
                    
                    .preview-title {
                        font-size: 3rem;
                        margin-bottom: 1rem;
                        text-shadow: 0 0 20px currentColor;
                        text-transform: uppercase;
                        letter-spacing: 2px;
                    }
                    
                    .preview-subtitle {
                        font-size: 1.5rem;
                        opacity: 0.8;
                    }
                    
                    .preview-content > * {
                        margin-bottom: 2rem;
                    }
                    
                    .preview-server-node {
                        background: rgba(0, 0, 0, 0.7);
                        border: 2px solid var(--accent-color, #00ffff);
                        border-radius: 15px;
                        padding: 2rem;
                        margin: 2rem auto;
                        max-width: 400px;
                        text-align: center;
                        box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
                        transition: all 0.3s ease;
                    }
                    
                    .preview-server-node:hover {
                        transform: scale(1.05);
                        box-shadow: 0 0 40px rgba(0, 255, 255, 0.5);
                    }
                    
                    .server-name {
                        font-size: 1.5rem;
                        color: var(--accent-color, #00ffff);
                        margin-bottom: 1rem;
                        font-weight: bold;
                        text-transform: uppercase;
                    }
                    
                    .server-address {
                        font-family: 'Courier New', monospace;
                        background: rgba(0, 0, 0, 0.9);
                        color: #ffffff;
                        padding: 1rem 1.5rem;
                        border-radius: 8px;
                        border: 2px solid var(--accent-color, #00ffff);
                        margin: 1rem 0;
                        font-weight: bold;
                        text-shadow: 0 0 10px var(--accent-color, #00ffff);
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    
                    .server-address:hover {
                        background: rgba(0, 255, 255, 0.1);
                        transform: scale(1.02);
                    }
                    
                    .server-info {
                        color: rgba(255, 255, 255, 0.8);
                        margin-top: 1rem;
                        font-size: 0.9rem;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }
                    
                    .player-count {
                        margin-top: 1rem;
                        padding: 0.5rem 1rem;
                        background: rgba(0, 255, 0, 0.1);
                        border: 1px solid rgba(0, 255, 0, 0.5);
                        border-radius: 20px;
                        display: inline-flex;
                        align-items: center;
                        gap: 0.5rem;
                        color: #00ff00;
                    }
                    
                    .quantum-button {
                        background: transparent;
                        border: 2px solid var(--accent-color, #00ffff);
                        color: var(--accent-color, #00ffff);
                        padding: 1rem 2rem;
                        font-family: inherit;
                        font-weight: bold;
                        cursor: pointer;
                        border-radius: 5px;
                        transition: all 0.3s ease;
                        margin: 0.5rem;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                    }
                    
                    .quantum-button:hover:not(:disabled) {
                        background: var(--accent-color, #00ffff);
                        color: #0a0a0a;
                        box-shadow: 0 0 20px var(--accent-color, #00ffff);
                        transform: translateY(-2px);
                    }
                    
                    .quantum-button:disabled {
                        opacity: 0.7;
                        cursor: default;
                    }
                    
                    .quantum-button.secondary {
                        border-color: #888;
                        color: #ccc;
                    }
                    
                    .quantum-button.secondary:hover:not(:disabled) {
                        background: #888;
                        color: #000;
                        box-shadow: 0 0 20px rgba(136, 136, 136, 0.5);
                    }
                    
                    .quantum-button.danger {
                        border-color: #ff0000;
                        color: #ff0000;
                    }
                    
                    .quantum-button.danger:hover:not(:disabled) {
                        background: #ff0000;
                        color: #000;
                        box-shadow: 0 0 20px rgba(255, 0, 0, 0.5);
                    }
                    
                    .preview-button-container {
                        text-align: center;
                        margin: 2rem 0;
                    }
                    
                    .preview-image-container {
                        text-align: center;
                        margin: 2rem 0;
                    }
                    
                    .preview-image-container img {
                        border-radius: 10px;
                        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
                    }
                    
                    .image-caption {
                        margin-top: 1rem;
                        font-style: italic;
                        opacity: 0.8;
                    }
                    
                    .preview-placeholder {
                        text-align: center;
                        padding: 3rem;
                        color: rgba(255, 255, 255, 0.5);
                        border: 2px dashed rgba(255, 255, 255, 0.3);
                        border-radius: 8px;
                        margin: 2rem 0;
                    }
                    
                    .preview-text-block {
                        margin: 2rem 0;
                        line-height: 1.6;
                        padding: 0 1rem;
                    }
                    
                    .preview-text-block h1, 
                    .preview-text-block h2, 
                    .preview-text-block h3 {
                        color: var(--accent-color, #00ffff);
                        text-shadow: 0 0 10px currentColor;
                        margin: 1.5rem 0 1rem;
                    }
                    
                    .preview-text-block h1 { font-size: 2.5rem; }
                    .preview-text-block h2 { font-size: 2rem; }
                    .preview-text-block h3 { font-size: 1.5rem; }
                    
                    .preview-text-block p {
                        margin-bottom: 1rem;
                    }
                    
                    .preview-text-block ul,
                    .preview-text-block ol {
                        margin: 1rem 0 1rem 2rem;
                    }
                    
                    .preview-text-block li {
                        margin-bottom: 0.5rem;
                    }
                    
                    .preview-text-block a {
                        color: var(--accent-color, #00ffff);
                        text-decoration: none;
                        border-bottom: 1px solid currentColor;
                        transition: all 0.3s ease;
                    }
                    
                    .preview-text-block a:hover {
                        color: #ffffff;
                        text-shadow: 0 0 10px var(--accent-color, #00ffff);
                    }
                    
                    .preview-text-block strong {
                        color: var(--accent-color, #00ffff);
                    }
                    
                    .preview-text-block em {
                        color: rgba(255, 255, 255, 0.9);
                    }
                    
                    /* Matrix background effect simulation */
                    body::before {
                        content: '';
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="20" font-family="monospace" font-size="8" fill="%2300ffff" opacity="0.1">01001000</text></svg>');
                        opacity: 0.05;
                        pointer-events: none;
                        z-index: -1;
                    }
                    
                    /* Preview mode indicator */
                    body::after {
                        content: '🔍 PREVIEW MODE';
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        background: rgba(0, 0, 0, 0.8);
                        padding: 10px 15px;
                        border-radius: 5px;
                        border: 1px solid #00ffff;
                        font-size: 12px;
                        z-index: 1001;
                    }
                    
                    /* Copy notification */
                    .copy-notification-preview {
                        position: fixed;
                        bottom: 20px;
                        right: 20px;
                        background: linear-gradient(45deg, #00ffff, #ff00ff);
                        color: #000;
                        padding: 15px 25px;
                        border-radius: 8px;
                        font-weight: bold;
                        transform: translateY(100px);
                        opacity: 0;
                        transition: all 0.3s ease;
                        z-index: 1002;
                    }
                    
                    .copy-notification-preview.show {
                        transform: translateY(0);
                        opacity: 1;
                    }
                </style>
            </head>
            <body>
                ${previewContent}
                <script>
                    // Add copy functionality
                    document.querySelectorAll('.server-address').forEach(address => {
                        address.addEventListener('click', function(e) {
                            e.preventDefault();
                            const text = this.textContent;
                            if (navigator.clipboard) {
                                navigator.clipboard.writeText(text).then(() => {
                                    const notification = document.createElement('div');
                                    notification.className = 'copy-notification-preview';
                                    notification.textContent = 'Copied: ' + text;
                                    document.body.appendChild(notification);
                                    
                                    setTimeout(() => {
                                        notification.classList.add('show');
                                    }, 10);
                                    
                                    setTimeout(() => {
                                        notification.classList.remove('show');
                                        setTimeout(() => notification.remove(), 300);
                                    }, 2000);
                                });
                            }
                        });
                    });
                </script>
            </body>
            </html>
        `;
    }

    clearPreview() {
        const previewContent = document.getElementById('previewContent');
        if (previewContent) {
            previewContent.innerHTML = `
                <div class="preview-placeholder">
                    <div class="placeholder-icon">👁️</div>
                    <p>Live preview will appear here when you edit a page</p>
                </div>
            `;
        }
    }

    // Page Management Operations
    duplicatePage(pageId, event) {
        if (event) {
            event.stopPropagation();
        }

        const duplicatedPage = window.siteBuilderCore.duplicatePage(pageId);
        if (duplicatedPage) {
            this.renderPagesList();
            this.selectPage(duplicatedPage.id);
            this.updateSiteStats();
        }
    }

    confirmDeletePage(pageId, event) {
        if (event) {
            event.stopPropagation();
        }

        const page = window.siteBuilderCore.getPage(pageId);
        if (!page) return;

        const modal = document.getElementById('deleteConfirmModal');
        const pageNameEl = document.getElementById('deletePageName');
        
        if (modal && pageNameEl) {
            pageNameEl.textContent = page.title;
            modal.classList.add('active');
            
            // Store the page ID for deletion
            modal.dataset.deletePageId = pageId;
        }
    }

    previewPage(pageId, event) {
        if (event) {
            event.stopPropagation();
        }

        this.selectPage(pageId);
        if (!this.previewMode) {
            this.togglePreview();
        }
    }

    // Section Collapsing
    toggleSection(titleElement, sectionType) {
        const section = titleElement.closest('.collapsible-section');
        const content = section.querySelector('.section-content');
        const toggle = titleElement.querySelector('.section-toggle');
        
        const isCollapsed = content.style.display === 'none';
        content.style.display = isCollapsed ? 'block' : 'none';
        toggle.textContent = isCollapsed ? '▼' : '▶';
        section.classList.toggle('collapsed', !isCollapsed);
        
        // Save collapse state
        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (page) {
            page[`_${sectionType}Collapsed`] = !isCollapsed;
        }
    }

    // Text Formatting Tools
    insertTextFormat(blockIndex, format) {
        const textarea = document.querySelector(`.content-block[data-index="${blockIndex}"] .content-editor-area`);
        if (!textarea) return;

        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = textarea.value.substring(start, end);
        let replacement = '';

        switch (format) {
            case 'bold':
                replacement = `<strong>${selectedText || 'Bold text'}</strong>`;
                break;
            case 'italic':
                replacement = `<em>${selectedText || 'Italic text'}</em>`;
                break;
            case 'link':
                const url = prompt('Enter URL:', 'https://');
                if (url) {
                    replacement = `<a href="${url}" target="_blank">${selectedText || 'Link text'}</a>`;
                } else {
                    return;
                }
                break;
            case 'heading':
                replacement = `<h3>${selectedText || 'Heading'}</h3>`;
                break;
        }

        textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
        textarea.focus();
        textarea.setSelectionRange(start, start + replacement.length);
        
        // Trigger input event to update content
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
    }

    validateHTML(blockIndex) {
        const textarea = document.querySelector(`.content-block[data-index="${blockIndex}"] .custom-html`);
        if (!textarea) return;

        const html = textarea.value;
        
        // Basic HTML validation
        try {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            
            // Check for script tags
            if (tempDiv.querySelector('script')) {
                alert('⚠️ Script tags are not allowed for security reasons.');
                return;
            }
            
            // Check for event handlers
            if (html.match(/on\w+\s*=/i)) {
                alert('⚠️ Event handlers (onclick, onload, etc.) are not allowed for security reasons.');
                return;
            }
            
            // Check for unclosed tags
            const openTags = html.match(/<[^/>][^>]*>/g) || [];
            const closeTags = html.match(/<\/[^>]+>/g) || [];
            
            if (openTags.length !== closeTags.length) {
                window.siteBuilderCore.showMessage('⚠️ Warning: Possible unclosed HTML tags detected', 'warning');
            } else {
                window.siteBuilderCore.showMessage('✅ HTML is valid!', 'success');
            }
        } catch (error) {
            alert('❌ Invalid HTML: ' + error.message);
        }
    }

    formatHTML(blockIndex) {
        const textarea = document.querySelector(`.content-block[data-index="${blockIndex}"] .custom-html`);
        if (!textarea) return;

        // Simple HTML formatting
        let html = textarea.value;
        
        // Remove extra whitespace
        html = html.replace(/>\s+</g, '><');
        html = html.replace(/\s+/g, ' ');
        
        // Add newlines and indentation
        html = html.replace(/></g, '>\n<');
        
        // Basic indentation
        const lines = html.split('\n');
        let indentLevel = 0;
        const formatted = lines.map(line => {
            line = line.trim();
            
            // Decrease indent for closing tags
            if (line.match(/^<\//) || line.match(/\/>/)) {
                indentLevel = Math.max(0, indentLevel - 1);
            }
            
            const indented = '  '.repeat(indentLevel) + line;
            
            // Increase indent for opening tags (not self-closing)
            if (line.match(/^<[^\/]/) && !line.match(/\/>/) && !line.match(/<(br|hr|img|input|meta|link)/i)) {
                indentLevel++;
            }
            
            return indented;
        }).join('\n');
        
        textarea.value = formatted;
        
        // Trigger input event
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
    }

    previewHTML(blockIndex) {
        const textarea = document.querySelector(`.content-block[data-index="${blockIndex}"] .custom-html`);
        if (!textarea) return;

        const html = textarea.value;
        const previewWindow = window.open('', 'htmlPreview', 'width=800,height=600,scrollbars=yes,resizable=yes');
        
        previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>HTML Preview</title>
                <style>
                    body { 
                        font-family: Arial, sans-serif; 
                        padding: 20px;
                        background: #f5f5f5;
                    }
                    .preview-header { 
                        background: #333;
                        color: white;
                        padding: 10px; 
                        margin: -20px -20px 20px -20px;
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <div class="preview-header">
                    HTML Preview - Close this window when done
                </div>
                ${html}
            </body>
            </html>
        `);
        previewWindow.document.close();
    }

    // Icon and Color Pickers
    showIconPicker() {
        const commonIcons = [
            '🏠', '🎮', '🎯', '⚔️', '🛡️', '🏰', '🎪', '🎨', '🎭', '🎬',
            '🚀', '🛸', '🌟', '⭐', '✨', '💫', '🌈', '🔥', '❄️', '⚡',
            '💎', '💰', '🏆', '🥇', '🎖️', '🏅', '👑', '🔮', '🎲', '🎰',
            '📢', '📣', '📡', '📺', '📻', '🎙️', '🎧', '🎵', '🎶', '🎸',
            '🖥️', '💻', '⌨️', '🖱️', '🕹️', '📱', '☎️', '📞', '📟', '📠',
            '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🔩', '⚙️', '🗜️', '⚗️', '🧪',
            '💬', '💭', '🗨️', '🗯️', '💢', '💥', '💦', '💨', '🕸️', '🦠',
            '👥', '👤', '🗣️', '👣', '🧠', '👁️', '👀', '👂', '👃', '👅',
            '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
            '📈', '📊', '📉', '📐', '📏', '📌', '📍', '📎', '🔗', '⛓️',
            '🏴', '🏳️', '🚩', '🏁', '🏴‍☠️', '🎌', '🎏', '🎐', '🎀', '🎁',
            '🌐', '🌍', '🌎', '🌏', '🗺️', '🗾', '🧭', '🏔️', '⛰️', '🌋',
            '🏖️', '🏜️', '🏝️', '🏞️', '🏟️', '🏛️', '🏗️', '🏘️', '🏚️', '🏙️',
            '🦄', '🐉', '🦕', '🦖', '🦈', '🐙', '🦑', '🦐', '🦞', '🦀',
            '🧟', '🧟‍♂️', '🧟‍♀️', '🧛', '🧛‍♂️', '🧛‍♀️', '🧙', '🧙‍♂️', '🧙‍♀️', '🧚',
            '⚡', '🔥', '💧', '🌊', '🍃', '🌸', '🌺', '🌻', '🌹', '🥀',
            '🎃', '👻', '💀', '☠️', '👽', '👾', '🤖', '🎅', '🎄', '🎆',
            '🍕', '🍔', '🍟', '🌭', '🥪', '🌮', '🌯', '🥙', '🧆', '🥘',
            '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
            '🏹', '🎣', '🥊', '🥋', '🎽', '🛹', '🛼', '🛷', '⛸️', '🥌',
            '🎪', '🎨', '🎬', '🎤', '🎧', '🎼', '🎹', '🥁', '🎷', '🎺',
            '🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐',
            '✈️', '🛫', '🛬', '🛩️', '💺', '🚁', '🚟', '🚠', '🚡', '🛰️',
            '⏰', '⏱️', '⏲️', '🕰️', '🕛', '🕧', '🕐', '🕜', '🕑', '🕝'
        ];

        const modal = document.createElement('div');
        modal.className = 'icon-picker-modal';
        modal.innerHTML = `
            <div class="icon-picker-content">
                <div class="icon-picker-header">
                    <h3>Choose an Icon</h3>
                    <button class="close-picker" onclick="this.closest('.icon-picker-modal').remove()">✕</button>
                </div>
                <div class="icon-picker-search">
                    <input type="text" placeholder="Search icons..." onkeyup="siteBuilderUI.filterIcons(this.value)">
                </div>
                <div class="icon-picker-grid">
                    ${commonIcons.map(icon => `
                        <div class="icon-option" onclick="siteBuilderUI.selectIcon('${icon}')">${icon}</div>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    filterIcons(searchTerm) {
        const icons = document.querySelectorAll('.icon-option');
        icons.forEach(icon => {
            icon.style.display = icon.textContent.includes(searchTerm) ? 'flex' : 'none';
        });
    }

    selectIcon(icon) {
        const iconInput = document.getElementById('editPageIcon');
        if (iconInput) {
            iconInput.value = icon;
            iconInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        // Remove picker modal
        const modal = document.querySelector('.icon-picker-modal');
        if (modal) modal.remove();
    }

    showColorPresets() {
        const presets = [
            { name: 'Cyan', color: '#00ffff' },
            { name: 'Magenta', color: '#ff00ff' },
            { name: 'Yellow', color: '#ffff00' },
            { name: 'Lime', color: '#00ff00' },
            { name: 'Orange', color: '#ff6600' },
            { name: 'Pink', color: '#ff6b9d' },
            { name: 'Purple', color: '#c77dff' },
            { name: 'Blue', color: '#0066ff' },
            { name: 'Red', color: '#ff0066' },
            { name: 'Green', color: '#00cc66' },
            { name: 'Gold', color: '#ffd700' },
            { name: 'Silver', color: '#c0c0c0' },
            { name: 'Teal', color: '#008080' },
            { name: 'Coral', color: '#ff7f50' },
            { name: 'Violet', color: '#ee82ee' },
            { name: 'Indigo', color: '#4b0082' },
            { name: 'Turquoise', color: '#40e0d0' },
            { name: 'Crimson', color: '#dc143c' },
            { name: 'Electric', color: '#00d4ff' },
            { name: 'Neon Green', color: '#39ff14' }
        ];

        const modal = document.createElement('div');
        modal.className = 'color-presets-modal';
        modal.innerHTML = `
            <div class="color-presets-content">
                <div class="color-presets-header">
                    <h3>Color Presets</h3>
                    <button class="close-presets" onclick="this.closest('.color-presets-modal').remove()">✕</button>
                </div>
                <div class="color-presets-grid">
                    ${presets.map(preset => `
                        <div class="color-preset" onclick="siteBuilderUI.selectColorPreset('${preset.color}')">
                            <div class="color-swatch" style="background: ${preset.color}"></div>
                            <div class="color-name">${preset.name}</div>
                            <div class="color-value">${preset.color}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    selectColorPreset(color) {
        const colorInput = document.getElementById('editAccentColor');
        const colorTextInput = document.getElementById('editAccentColorText');
        
        if (colorInput) {
            colorInput.value = color;
            colorInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        if (colorTextInput) {
            colorTextInput.value = color;
        }
        
        // Remove modal
        const modal = document.querySelector('.color-presets-modal');
        if (modal) modal.remove();
    }

    showContentTemplates() {
        const templates = window.siteBuilderTemplates?.getContentBlockTemplates() || {};
        
        const modal = document.createElement('div');
        modal.className = 'content-templates-modal';
        modal.innerHTML = `
            <div class="content-templates-content">
                <div class="content-templates-header">
                    <h3>Content Block Templates</h3>
                    <button class="close-templates" onclick="this.closest('.content-templates-modal').remove()">✕</button>
                </div>
                <div class="content-templates-grid">
                    ${Object.entries(templates).map(([key, template]) => `
                        <div class="content-template" onclick="siteBuilderUI.insertContentTemplate('${key}')">
                            <div class="template-name">${template.name}</div>
                            <div class="template-preview">${template.type}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    insertContentTemplate(templateKey) {
        const templates = window.siteBuilderTemplates?.getContentBlockTemplates() || {};
        const template = templates[templateKey];
        
        if (!template) return;

        const page = window.siteBuilderCore.getPage(this.currentPageId);
        if (!page) return;

        const newBlock = {
            type: template.type,
            content: template.content
        };

        page.content = page.content || [];
        page.content.push(newBlock);

        this.markUnsavedChanges();
        this.renderPageEditor(this.currentPageId);
        this.updatePreview();

        // Remove modal
        const modal = document.querySelector('.content-templates-modal');
        if (modal) modal.remove();

        // Scroll to new block
        setTimeout(() => {
            const newBlockElement = document.querySelector(`[data-index="${page.content.length - 1}"]`);
            if (newBlockElement) {
                newBlockElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                newBlockElement.classList.add('highlight-new');
                setTimeout(() => newBlockElement.classList.remove('highlight-new'), 2000);
            }
        }, 100);
    }

    // Utility Functions
    updateSiteStats() {
        const totalPagesEl = document.getElementById('totalPages');
        const lastSavedEl = document.getElementById('lastSaved');
        
        if (totalPagesEl && window.siteBuilderCore) {
            totalPagesEl.textContent = window.siteBuilderCore.pages.length;
        }
        
        if (lastSavedEl) {
            const lastModified = localStorage.getItem('xdream-site-builder-last-save');
            if (lastModified) {
                const date = new Date(lastModified);
                const now = new Date();
                const diffMinutes = Math.floor((now - date) / 60000);
                
                if (diffMinutes < 1) {
                    lastSavedEl.textContent = 'Just now';
                } else if (diffMinutes < 60) {
                    lastSavedEl.textContent = `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
                } else {
                    lastSavedEl.textContent = date.toLocaleTimeString();
                }
            } else {
                lastSavedEl.textContent = 'Never';
            }
        }
    }

    setupAutoSaveIndicator() {
        // Create auto-save indicator
        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator';
        indicator.id = 'autoSaveIndicator';
        indicator.innerHTML = '<span class="save-icon">💾</span><span class="save-text">All changes saved</span>';
        document.body.appendChild(indicator);
    }

    // Enhanced Event Listeners and Setup
    setupEventListeners() {
        this.setupBasicEventListeners();
        this.setupModalEventListeners();
        this.setupTemplateEventListeners();
        this.setupClosePreviewListener();
        
        // Update stats periodically
        setInterval(() => this.updateSiteStats(), 30000);
        
        console.log('🎧 Complete UI event listeners setup');
    }

    setupBasicEventListeners() {
        // Add Page button
        const addPageBtn = document.getElementById('addPageBtn');
        if (addPageBtn) {
            addPageBtn.addEventListener('click', () => this.showAddPageModal());
        }

        // Save Page button  
        const savePageBtn = document.getElementById('savePageBtn');
        if (savePageBtn) {
            savePageBtn.addEventListener('click', () => this.saveCurrentPage());
        }

        // Delete Page button
        const deletePageBtn = document.getElementById('deletePageBtn');
        if (deletePageBtn) {
            deletePageBtn.addEventListener('click', () => {
                if (this.currentPageId) {
                    this.confirmDeletePage(this.currentPageId);
                }
            });
        }

        // Preview button
        const previewBtn = document.getElementById('previewBtn');
        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.togglePreview());
        }

        // Close preview button
        const closePreviewBtn = document.getElementById('closePreviewBtn');
        if (closePreviewBtn) {
            closePreviewBtn.addEventListener('click', () => this.togglePreview());
        }

        // Handle URL hash changes
        window.addEventListener('hashchange', () => {
            const hash = window.location.hash;
            if (hash.startsWith('#page-')) {
                const pageId = hash.replace('#page-', '');
                if (pageId !== this.currentPageId) {
                    this.selectPage(pageId);
                }
            }
        });

        // Check initial hash
        if (window.location.hash.startsWith('#page-')) {
            const pageId = window.location.hash.replace('#page-', '');
            setTimeout(() => this.selectPage(pageId), 100);
        }
    }

    setupModalEventListeners() {
        // Add Page Form
        const addPageForm = document.getElementById('addPageForm');
        if (addPageForm) {
            addPageForm.addEventListener('submit', (e) => this.handleAddPageSubmit(e));
        }

        // Delete Confirmation
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', () => this.handleDeletePage());
        }

        // Modal backdrop clicks
        document.querySelectorAll('.modal-overlay').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal(modal.id);
                }
            });
        });
    }

    setupTemplateEventListeners() {
        // Template selection in add page modal
        const pageTemplateSelect = document.getElementById('pageTemplate');
        if (pageTemplateSelect) {
            pageTemplateSelect.addEventListener('change', (e) => {
                const templateName = e.target.value;
                if (templateName && window.siteBuilderTemplates) {
                    const template = window.siteBuilderTemplates.getTemplate(templateName);
                    if (template) {
                        // Update accent color based on template
                        const accentColorInput = document.getElementById('accentColor');
                        if (accentColorInput) {
                            accentColorInput.value = template.accentColor;
                        }
                    }
                }
            });
        }
    }

    setupClosePreviewListener() {
        // ESC key to close preview
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.previewMode) {
                this.togglePreview();
            }
        });
    }

    setupFormEventListeners(page) {
        this.setupBasicInfoListeners(page);
        this.setupSEOListeners(page);
        this.setupEffectsListeners(page);
        this.setupContentBlockListeners(page);
    }

    setupBasicInfoListeners(page) {
        const titleInput = document.getElementById('editPageTitle');
        const subtitleInput = document.getElementById('editPageSubtitle');
        const iconInput = document.getElementById('editPageIcon');
        const colorInput = document.getElementById('editAccentColor');
        const colorTextInput = document.getElementById('editAccentColorText');

        if (titleInput) {
            titleInput.addEventListener('input', (e) => {
                page.title = e.target.value;
                this.markUnsavedChanges();
                this.renderPagesList();
                this.updatePreview();
                
                // Update editor title
                const editorTitle = document.getElementById('editorTitle');
                if (editorTitle) {
                    editorTitle.innerHTML = `
                        Editing: <span style="color: ${page.accentColor}">${this.escapeHtml(page.title)}</span>
                        <span class="page-status">${page.isDefault ? ' (Default Page)' : ''}</span>
                    `;
                }
            });
        }

        if (subtitleInput) {
            subtitleInput.addEventListener('input', (e) => {
                page.subtitle = e.target.value;
                this.markUnsavedChanges();
                this.renderPagesList();
                this.updatePreview();
            });
        }

        if (iconInput) {
            iconInput.addEventListener('input', (e) => {
                page.icon = e.target.value.slice(0, 2); // Limit to 2 characters
                this.markUnsavedChanges();
                this.renderPagesList();
                this.updateIconPreview();
                this.updatePreview();
            });
        }

        if (colorInput && colorTextInput) {
            const updateColor = (color) => {
                if (/^#[0-9A-Fa-f]{6}$/.test(color)) {
                    page.accentColor = color;
                    this.markUnsavedChanges();
                    this.renderPagesList();
                    this.updateColorPreview();
                    this.updatePreview();
                    
                    // Update editor title color
                    const titleSpan = document.querySelector('#editorTitle span');
                    if (titleSpan) {
                        titleSpan.style.color = color;
                    }
                }
            };

            colorInput.addEventListener('input', (e) => {
                colorTextInput.value = e.target.value;
                updateColor(e.target.value);
            });

            colorTextInput.addEventListener('input', (e) => {
                if (/^#[0-9A-Fa-f]{6}$/.test(e.target.value)) {
                    colorInput.value = e.target.value;
                    updateColor(e.target.value);
                }
            });
        }
    }

    setupSEOListeners(page) {
        const descInput = document.getElementById('editPageDescription');
        const keywordsInput = document.getElementById('editPageKeywords');

        if (descInput) {
            const updateCharCount = () => {
                const charCount = document.getElementById('descCharCount');
                if (charCount) {
                    charCount.textContent = descInput.value.length;
                    charCount.style.color = descInput.value.length > 160 ? '#ff0000' : '#00ffff';
                }
            };

            descInput.addEventListener('input', (e) => {
                page.seo = page.seo || {};
                page.seo.description = e.target.value;
                this.markUnsavedChanges();
                updateCharCount();
            });
            
            updateCharCount(); // Initial count
        }

        if (keywordsInput) {
            keywordsInput.addEventListener('input', (e) => {
                page.seo = page.seo || {};
                page.seo.keywords = e.target.value;
                this.markUnsavedChanges();
            });
        }
    }

    setupEffectsListeners(page) {
        const particlesCheck = document.getElementById('editParticles');
        const matrixCheck = document.getElementById('editMatrix');
        const glitchCheck = document.getElementById('editGlitch');

        const updateEffects = () => {
            page.backgroundEffects = {
                particles: particlesCheck?.checked || false,
                matrix: matrixCheck?.checked || false,
                glitch: glitchCheck?.checked || false
            };
            this.markUnsavedChanges();
            this.updatePreview();
        };

        [particlesCheck, matrixCheck, glitchCheck].forEach(checkbox => {
            if (checkbox) {
                checkbox.addEventListener('change', updateEffects);
            }
        });
    }

    setupContentBlockListeners(page) {
        const contentBlocks = document.querySelectorAll('.content-block');
        
        contentBlocks.forEach((block, blockIndex) => {
            const type = block.dataset.type;
            this.setupBlockTypeListeners(block, blockIndex, type, page);
        });
    }

    setupBlockTypeListeners(block, index, type, page) {
        if (!page.content || !page.content[index]) return;

        const updateContent = () => {
            this.markUnsavedChanges();
            this.updatePreview();
        };

        // Store listener cleanup functions
        const listeners = [];

        switch (type) {
            case 'text':
            case 'custom':
                const textarea = block.querySelector('.content-editor-area');
                if (textarea) {
                    const handler = (e) => {
                        page.content[index].content = e.target.value;
                        updateContent();
                    };
                    textarea.addEventListener('input', handler);
                    listeners.push(() => textarea.removeEventListener('input', handler));
                }
                break;

            case 'server-node':
                this.setupServerNodeListeners(block, index, page, updateContent, listeners);
                break;

            case 'button':
                this.setupButtonListeners(block, index, page, updateContent, listeners);
                break;

            case 'image':
                this.setupImageListeners(block, index, page, updateContent, listeners);
                break;
        }

        // Store cleanup function
        this.contentChangeListeners.set(`block-${index}`, () => {
            listeners.forEach(cleanup => cleanup());
        });
    }

    clearContentChangeListeners() {
        this.contentChangeListeners.forEach(cleanup => cleanup());
        this.contentChangeListeners.clear();
    }

    setupServerNodeListeners(block, index, page, updateContent, listeners) {
        const nameInput = block.querySelector('.server-name');
        const addressInput = block.querySelector('.server-address');
        const infoInput = block.querySelector('.server-info');
        const playerCountCheck = block.querySelector('.show-player-count');

        if (nameInput) {
            const handler = (e) => {
                page.content[index].serverName = e.target.value;
                updateContent();
            };
            nameInput.addEventListener('input', handler);
            listeners.push(() => nameInput.removeEventListener('input', handler));
        }

        if (addressInput) {
            const handler = (e) => {
                page.content[index].serverAddress = e.target.value;
                updateContent();
            };
            addressInput.addEventListener('input', handler);
            listeners.push(() => addressInput.removeEventListener('input', handler));
        }

        if (infoInput) {
            const handler = (e) => {
                page.content[index].serverInfo = e.target.value;
                updateContent();
            };
            infoInput.addEventListener('input', handler);
            listeners.push(() => infoInput.removeEventListener('input', handler));
        }

        if (playerCountCheck) {
            const handler = (e) => {
                page.content[index].showPlayerCount = e.target.checked;
                updateContent();
            };
            playerCountCheck.addEventListener('change', handler);
            listeners.push(() => playerCountCheck.removeEventListener('change', handler));
        }
    }

    setupButtonListeners(block, index, page, updateContent, listeners) {
        const textInput = block.querySelector('.button-text');
        const urlInput = block.querySelector('.button-url');
        const styleSelect = block.querySelector('.button-style');
        const newTabCheck = block.querySelector('.button-new-tab');

        if (textInput) {
            const handler = (e) => {
                page.content[index].text = e.target.value;
                this.updateButtonPreview(block, page.content[index]);
                updateContent();
            };
            textInput.addEventListener('input', handler);
            listeners.push(() => textInput.removeEventListener('input', handler));
        }

        if (urlInput) {
            const handler = (e) => {
                page.content[index].url = e.target.value;
                updateContent();
            };
            urlInput.addEventListener('input', handler);
            listeners.push(() => urlInput.removeEventListener('input', handler));
        }

        if (styleSelect) {
            const handler = (e) => {
                page.content[index].style = e.target.value;
                this.updateButtonPreview(block, page.content[index]);
                updateContent();
            };
            styleSelect.addEventListener('change', handler);
            listeners.push(() => styleSelect.removeEventListener('change', handler));
        }

        if (newTabCheck) {
            const handler = (e) => {
                page.content[index].newTab = e.target.checked;
                updateContent();
            };
            newTabCheck.addEventListener('change', handler);
            listeners.push(() => newTabCheck.removeEventListener('change', handler));
        }
    }

    setupImageListeners(block, index, page, updateContent, listeners) {
        const srcInput = block.querySelector('.image-src');
        const altInput = block.querySelector('.image-alt');
        const captionInput = block.querySelector('.image-caption');

        if (srcInput) {
            const handler = (e) => {
                page.content[index].src = e.target.value;
                this.updateImagePreview(block, page.content[index]);
                updateContent();
            };
            srcInput.addEventListener('input', handler);
            listeners.push(() => srcInput.removeEventListener('input', handler));
        }

        if (altInput) {
            const handler = (e) => {
                page.content[index].alt = e.target.value;
                updateContent();
            };
            altInput.addEventListener('input', handler);
            listeners.push(() => altInput.removeEventListener('input', handler));
        }

        if (captionInput) {
            const handler = (e) => {
                page.content[index].caption = e.target.value;
                updateContent();
            };
            captionInput.addEventListener('input', handler);
            listeners.push(() => captionInput.removeEventListener('input', handler));
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S: Save current page
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveCurrentPage();
            }
            
            // Ctrl+N: New page
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                this.showAddPageModal();
            }
            
            // Ctrl+P: Preview
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                if (this.currentPageId) {
                    this.togglePreview();
                }
            }
            
            // Ctrl+D: Duplicate current page
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                if (this.currentPageId) {
                    this.duplicatePage(this.currentPageId);
                }
            }
            
            // Ctrl+Shift+S: Save all
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                window.siteBuilderCore.saveData();
            }
        });
        
        console.log('⌨️ Keyboard shortcuts enabled');
    }

    setupDragAndDrop() {
        // Make content blocks draggable
        document.addEventListener('dragstart', (e) => {
            if (e.target.closest('.content-block')) {
                this.isDragging = true;
            }
        });

        document.addEventListener('dragend', (e) => {
            this.isDragging = false;
            // Remove any dragging classes
            document.querySelectorAll('.dragging').forEach(el => {
                el.classList.remove('dragging');
            });
        });

        console.log('🖱️ Drag and drop functionality ready');
    }

    // Form Handlers
    handleAddPageSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const pageData = {
            title: formData.get('pageTitle'),
            subtitle: formData.get('pageSubtitle'),
            icon: formData.get('pageIcon'),
            template: formData.get('pageTemplate'),
            accentColor: formData.get('accentColor')
        };

        const newPage = window.siteBuilderCore.createPage(pageData);
        if (newPage) {
            this.renderPagesList();
            this.selectPage(newPage.id);
            this.updateSiteStats();
            closeModal('addPageModal');
            e.target.reset();
        }
    }

    handleDeletePage() {
        const modal = document.getElementById('deleteConfirmModal');
        const pageId = modal?.dataset.deletePageId;
        
        if (pageId && window.siteBuilderCore.deletePage(pageId)) {
            if (this.currentPageId === pageId) {
                this.currentPageId = null;
                this.clearEditor();
                this.clearPreview();
            }
            this.renderPagesList();
            this.updateEditorButtons();
            this.updateSiteStats();
        }
        closeModal('deleteConfirmModal');
    }

    // Modal Management
    showAddPageModal() {
        const modal = document.getElementById('addPageModal');
        if (modal) {
            modal.classList.add('active');
            // Focus on title input
            setTimeout(() => {
                const titleInput = document.getElementById('pageTitle');
                if (titleInput) titleInput.focus();
            }, 100);
        }
    }

    // Real-time preview updates
    updateIconPreview() {
        const iconPreview = document.getElementById('iconPreview');
        const iconInput = document.getElementById('editPageIcon');
        const colorInput = document.getElementById('editAccentColor');
        
        if (iconPreview && iconInput) {
            iconPreview.textContent = iconInput.value || '🎮';
            if (colorInput) {
                iconPreview.style.color = colorInput.value;
            }
        }
    }