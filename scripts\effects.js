// scripts/effects.js - Visual effects and animations with performance fixes

let matrixAnimationId = null;
let matrixCanvas = null;
let matrixCtx = null;
// MEMORY FIX: Track all active intervals and animations
let activeIntervals = new Set();
let activeTimeouts = new Set();
let activeAnimations = new Set();

function initializeEffects() {
    // Matrix background effect
    setupMatrixBackground();
    
    // Glitch effects
    addGlitchEffect();
    
    // Parallax effects (only on capable devices)
    setupParallaxEffects();
    
    // Quantum button effects
    setupQuantumButtons();
    
    // Konami code easter egg
    setupKonamiCode();
}

// PERFORMANCE OPTIMIZED: Matrix background effect
function setupMatrixBackground() {
    matrixCanvas = document.getElementById('matrix');
    if (!matrixCanvas) {
        console.error('Matrix canvas not found');
        return;
    }
    
    matrixCtx = matrixCanvas.getContext('2d');
    
    // Set initial size
    resizeMatrix();
    
    const matrixChars = '01';
    const fontSize = 14;
    let columns = Math.floor(matrixCanvas.width / fontSize);
    let drops = Array(columns).fill(1);
    let lastFrameTime = 0;
    const targetFPS = 30; // Limit to 30 FPS for performance
    const frameInterval = 1000 / targetFPS;

    function drawMatrix(currentTime) {
        // PERFORMANCE FIX: Frame rate limiting
        const deltaTime = currentTime - lastFrameTime;
        
        if (deltaTime < frameInterval) {
            matrixAnimationId = requestAnimationFrame(drawMatrix);
            return;
        }
        
        lastFrameTime = currentTime - (deltaTime % frameInterval);
        
        // CRITICAL FIX: Check if page is visible to save CPU
        if (!window.xDreamState?.isPageVisible) {
            matrixAnimationId = requestAnimationFrame(drawMatrix);
            return;
        }
        
        // PERFORMANCE FIX: Reduce opacity for better performance
        matrixCtx.fillStyle = 'rgba(0, 0, 0, 0.08)';
        matrixCtx.fillRect(0, 0, matrixCanvas.width, matrixCanvas.height);
        
        matrixCtx.fillStyle = '#00ffff';
        matrixCtx.font = fontSize + 'px monospace';
        
        // PERFORMANCE FIX: Process fewer drops for better performance
        const maxDrops = Math.min(drops.length, 50);
        for (let i = 0; i < maxDrops; i++) {
            const text = matrixChars[Math.floor(Math.random() * matrixChars.length)];
            matrixCtx.fillText(text, i * fontSize, drops[i] * fontSize);
            
            if (drops[i] * fontSize > matrixCanvas.height && Math.random() > 0.975) {
                drops[i] = 0;
            }
            drops[i]++;
        }
        
        matrixAnimationId = requestAnimationFrame(drawMatrix);
        activeAnimations.add(matrixAnimationId);
    }

    function resizeMatrix() {
        const dpr = window.devicePixelRatio || 1;
        const rect = matrixCanvas.getBoundingClientRect();
        
        // PERFORMANCE FIX: Use lower resolution on mobile
        const scale = window.innerWidth < 768 ? 0.5 : 1;
        
        matrixCanvas.width = rect.width * dpr * scale;
        matrixCanvas.height = rect.height * dpr * scale;
        matrixCtx.scale(dpr * scale, dpr * scale);
        
        // Recalculate columns and drops
        columns = Math.floor(matrixCanvas.width / fontSize);
        drops = Array(columns).fill(1);
    }

    // Start animation
    matrixAnimationId = requestAnimationFrame(drawMatrix);
    activeAnimations.add(matrixAnimationId);

    // PERFORMANCE FIX: Throttled resize handler
    let resizeTimeout;
    const resizeHandler = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(resizeMatrix, 250);
        activeTimeouts.add(resizeTimeout);
    };
    
    window.addEventListener('resize', resizeHandler);
    
    // MEMORY FIX: Store cleanup function
    matrixCanvas.cleanupFunction = () => {
        window.removeEventListener('resize', resizeHandler);
        if (resizeTimeout) clearTimeout(resizeTimeout);
        if (matrixAnimationId) cancelAnimationFrame(matrixAnimationId);
    };
}

// PERFORMANCE OPTIMIZED: Zombie particles with proper cleanup
function createZombieParticles() {
    const container = document.getElementById('zombieParticles');
    if (!container) return;
    
    // MEMORY FIX: Clear existing particles first
    clearParticles(container);
    
    // PERFORMANCE FIX: Reduce particle count on mobile
    const particleCount = window.innerWidth < 768 ? 6 : 12;
    const zombieEmojis = ['🧟', '🧟‍♂️', '🧟‍♀️', '💀', '🔥', '⚰️', '🩸'];
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.textContent = zombieEmojis[Math.floor(Math.random() * zombieEmojis.length)];
        particle.style.left = Math.random() * 100 + 'vw';
        particle.style.animationDelay = Math.random() * 10 + 's';
        particle.style.animationDuration = (Math.random() * 5 + 8) + 's';
        container.appendChild(particle);
    }
}

// NEW: Sand particles for Dune dimension
function createSandParticles() {
    const container = document.getElementById('sandParticles');
    if (!container) return;
    
    // MEMORY FIX: Clear existing particles first
    clearParticles(container);
    
    // PERFORMANCE FIX: Reduce particle count on mobile
    const particleCount = window.innerWidth < 768 ? 8 : 15;
    const sandEmojis = ['🏜️', '🌪️', '⏳', '🪱', '☀️', '💎', '⚱️'];
    
    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'sand-particle';
        particle.textContent = sandEmojis[Math.floor(Math.random() * sandEmojis.length)];
        particle.style.left = Math.random() * 100 + 'vw';
        particle.style.animationDelay = Math.random() * 12 + 's';
        particle.style.animationDuration = (Math.random() * 6 + 10) + 's';
        container.appendChild(particle);
    }
}

// MEMORY FIX: Helper to clear particles
function clearParticles(container) {
    while (container.firstChild) {
        container.removeChild(container.firstChild);
    }
}

// PERFORMANCE OPTIMIZED: Glitch effect with reduced frequency
function addGlitchEffect() {
    const glitchElements = document.querySelectorAll('.glitch');
    
    glitchElements.forEach(element => {
        let glitchInterval;
        
        function startGlitch() {
            // PERFORMANCE FIX: Reduce glitch frequency and check visibility
            glitchInterval = setInterval(() => {
                if (!window.xDreamState?.isPageVisible) return;
                
                if (Math.random() > 0.95) { // Reduced frequency
                    element.style.textShadow = `
                        ${Math.random() * 4 - 2}px ${Math.random() * 4 - 2}px 0 #ff00ff,
                        ${Math.random() * 4 - 2}px ${Math.random() * 4 - 2}px 0 #00ffff
                    `;
                    const resetTimeout = setTimeout(() => {
                        element.style.textShadow = '0 0 50px rgba(0, 255, 255, 0.5)';
                    }, 100);
                    activeTimeouts.add(resetTimeout);
                }
            }, 1000); // Increased interval for better performance
            
            activeIntervals.add(glitchInterval);
        }
        
        startGlitch();
        
        // MEMORY FIX: Store cleanup function
        element.cleanupFunction = () => {
            if (glitchInterval) {
                clearInterval(glitchInterval);
                activeIntervals.delete(glitchInterval);
            }
        };
    });
}

// PERFORMANCE OPTIMIZED: Parallax effect with throttling
function setupParallaxEffects() {
    // Only enable on devices with mouse and good performance
    if (!window.matchMedia('(hover: hover)').matches) return;
    if (window.innerWidth < 1024) return; // Disable on tablets/mobile
    
    let isThrottled = false;
    let animationFrameId = null;
    
    const mousemoveHandler = (e) => {
        if (isThrottled) return;
        
        isThrottled = true;
        
        // Cancel any existing animation frame
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
        }
        
        animationFrameId = requestAnimationFrame(() => {
            const mouseX = (e.clientX / window.innerWidth) * 2 - 1;
            const mouseY = (e.clientY / window.innerHeight) * 2 - 1;
            
            document.querySelectorAll('.game-portal').forEach((portal, index) => {
                const speed = (index + 1) * 0.3; // Reduced intensity
                portal.style.transform = `translate(${mouseX * speed}px, ${mouseY * speed}px)`;
            });
            
            isThrottled = false;
        });
        
        activeAnimations.add(animationFrameId);
    };
    
    document.addEventListener('mousemove', mousemoveHandler);
    
    // MEMORY FIX: Store cleanup function
    document.parallaxCleanup = () => {
        document.removeEventListener('mousemove', mousemoveHandler);
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            activeAnimations.delete(animationFrameId);
        }
    };
}

// PERFORMANCE OPTIMIZED: Quantum button effects with cleanup
function setupQuantumButtons() {
    document.querySelectorAll('.quantum-button').forEach(button => {
        const clickHandler = function(e) {
            // PERFORMANCE FIX: Prevent multiple ripples
            if (this.querySelector('.ripple-effect')) return;
            
            // Create ripple effect
            const ripple = document.createElement('div');
            ripple.className = 'ripple-effect';
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: radial-gradient(circle, rgba(0, 255, 255, 0.6), transparent);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out forwards;
                pointer-events: none;
                z-index: 0;
            `;
            
            // Ensure button has relative positioning
            if (getComputedStyle(this).position === 'static') {
                this.style.position = 'relative';
            }
            
            this.appendChild(ripple);
            
            // MEMORY FIX: Clean up ripple effect
            const cleanupTimeout = setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
            activeTimeouts.add(cleanupTimeout);
        };
        
        button.addEventListener('click', clickHandler);
        
        // MEMORY FIX: Store cleanup function
        button.cleanupFunction = () => {
            button.removeEventListener('click', clickHandler);
        };
    });
    
    // Add ripple animation styles if not present
    if (!document.querySelector('#ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// PERFORMANCE OPTIMIZED: Ambient effects with visibility checks
function startAmbientEffects() {
    const serverNodes = document.querySelectorAll('.server-node');
    let ambientInterval;
    let lastTime = Date.now();
    
    function updateAmbientGlow() {
        if (!window.xDreamState?.isPageVisible) return;
        
        const currentTime = Date.now();
        const deltaTime = (currentTime - lastTime) / 1000;
        lastTime = currentTime;
        
        serverNodes.forEach((node, index) => {
            const intensity = Math.sin(currentTime * 0.001 + index) * 0.2 + 0.8;
            let color = '0, 255, 255'; // Default cyan
            
            if (node.classList.contains('palworld-node')) {
                color = '255, 107, 157';
            } else if (node.classList.contains('sevendays-node')) {
                color = '255, 107, 53';
            } else if (node.classList.contains('zomboid-node')) {
                color = '255, 107, 107';
            } else if (node.classList.contains('dune-node')) {
                color = '255, 181, 53';
            }
            
            node.style.boxShadow = `0 0 ${intensity * 20}px rgba(${color}, ${intensity * 0.2})`;
        });
    }
    
    // PERFORMANCE FIX: Reduced frequency
    ambientInterval = setInterval(updateAmbientGlow, 100);
    activeIntervals.add(ambientInterval);
    
    // MEMORY FIX: Return cleanup function
    return () => {
        clearInterval(ambientInterval);
        activeIntervals.delete(ambientInterval);
    };
}

// Konami code easter egg with improved handling
function setupKonamiCode() {
    let konamiCode = [];
    const konami = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];
    let konamiTimeout;

    const keydownHandler = (e) => {
        // PERFORMANCE FIX: Ignore if typing in inputs
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;
        
        konamiCode.push(e.code);
        
        // MEMORY FIX: Reset timeout
        if (konamiTimeout) {
            clearTimeout(konamiTimeout);
            activeTimeouts.delete(konamiTimeout);
        }
        
        konamiTimeout = setTimeout(() => {
            konamiCode = [];
            activeTimeouts.delete(konamiTimeout);
        }, 5000);
        activeTimeouts.add(konamiTimeout);
        
        if (konamiCode.length > konami.length) {
            konamiCode.shift();
        }
        
        if (konamiCode.join(',') === konami.join(',')) {
            activateRainbowMode();
            konamiCode = [];
        }
    };
    
    document.addEventListener('keydown', keydownHandler);
    
    // MEMORY FIX: Store cleanup function
    document.konamiCleanup = () => {
        document.removeEventListener('keydown', keydownHandler);
        if (konamiTimeout) {
            clearTimeout(konamiTimeout);
            activeTimeouts.delete(konamiTimeout);
        }
    };
}

// PERFORMANCE OPTIMIZED: Rainbow mode with proper cleanup
function activateRainbowMode() {
    // Prevent multiple activations
    if (document.body.classList.contains('rainbow-mode')) return;
    
    document.body.classList.add('rainbow-mode');
    document.body.style.animation = 'rainbow 2s infinite';
    
    // Add rainbow animation styles
    const style = document.createElement('style');
    style.id = 'rainbow-styles';
    style.textContent = `
        @keyframes rainbow {
            0% { filter: hue-rotate(0deg); }
            100% { filter: hue-rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
    
    // Show notification if available
    if (typeof showNotification === 'function') {
        showNotification('🌈 KONAMI CODE ACTIVATED!', 'Enjoy the rainbow mode!');
    }
    
    // MEMORY FIX: Clean up after 10 seconds
    const cleanupTimeout = setTimeout(() => {
        document.body.style.animation = '';
        document.body.classList.remove('rainbow-mode');
        const rainbowStyles = document.querySelector('#rainbow-styles');
        if (rainbowStyles) {
            document.head.removeChild(rainbowStyles);
        }
        activeTimeouts.delete(cleanupTimeout);
    }, 10000);
    activeTimeouts.add(cleanupTimeout);
}

// PERFORMANCE FIX: Comprehensive cleanup function
function cleanupEffects() {
    // Cancel all animations
    activeAnimations.forEach(id => cancelAnimationFrame(id));
    activeAnimations.clear();
    
    // Clear all intervals
    activeIntervals.forEach(id => clearInterval(id));
    activeIntervals.clear();
    
    // Clear all timeouts
    activeTimeouts.forEach(id => clearTimeout(id));
    activeTimeouts.clear();
    
    // Cancel matrix animation
    if (matrixAnimationId) {
        cancelAnimationFrame(matrixAnimationId);
    }
    
    // Cleanup matrix canvas
    if (matrixCanvas && matrixCanvas.cleanupFunction) {
        matrixCanvas.cleanupFunction();
    }
    
    // Cleanup glitch effects
    document.querySelectorAll('.glitch').forEach(element => {
        if (element.cleanupFunction) {
            element.cleanupFunction();
        }
    });
    
    // Cleanup parallax
    if (document.parallaxCleanup) {
        document.parallaxCleanup();
    }
    
    // Cleanup quantum buttons
    document.querySelectorAll('.quantum-button').forEach(button => {
        if (button.cleanupFunction) {
            button.cleanupFunction();
        }
    });
    
    // Cleanup konami
    if (document.konamiCleanup) {
        document.konamiCleanup();
    }
    
    // Clear particle containers
    const zombieParticles = document.getElementById('zombieParticles');
    if (zombieParticles) clearParticles(zombieParticles);
    
    const sandParticles = document.getElementById('sandParticles');
    if (sandParticles) clearParticles(sandParticles);
}

// Auto-cleanup on page unload
window.addEventListener('beforeunload', cleanupEffects);
window.addEventListener('pagehide', cleanupEffects);