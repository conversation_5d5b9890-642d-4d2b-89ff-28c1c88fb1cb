// scripts/pwa.js - Progressive Web App functionality with update handling

let refreshing = false;

function initializePWA() {
    // Service Worker Registration with update handling
    registerServiceWorker();
    
    // Install Prompt
    handleInstallPrompt();
    
    // Offline/Online Status
    handleOnlineStatus();
    
    // Notifications
    requestNotificationPermission();
    
    // Cache control for development
    setupCacheControl();
}

// Service Worker Registration with update detection
function registerServiceWorker() {
    if ('serviceWorker' in navigator && location.protocol === 'https:') {
        window.addEventListener('load', () => {
            navigator.serviceWorker.register('/service-worker.js')
                .then(registration => {
                    console.log('🔧 Service Worker registered');
                    
                    // Check for updates on registration
                    registration.addEventListener('updatefound', () => {
                        console.log('🔄 Service Worker update found');
                        
                        const newWorker = registration.installing;
                        
                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                // New service worker is installed but not yet active
                                console.log('🆕 New Service Worker installed');
                                showUpdateNotification();
                            }
                        });
                    });
                    
                    // Check for updates every 5 minutes in development
                    if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                        setInterval(() => {
                            registration.update();
                        }, 300000); // 5 minutes
                    } else {
                        // Check for updates every 10 minutes in production
                        setInterval(() => {
                            registration.update();
                        }, 600000); // 10 minutes
                    }
                })
                .catch(error => {
                    console.log('❌ Service Worker registration failed:', error);
                });
        });
        
        // Handle controller change (when new SW takes over)
        navigator.serviceWorker.addEventListener('controllerchange', () => {
            if (!refreshing) {
                refreshing = true;
                console.log('🔄 Reloading page with new Service Worker');
                window.location.reload();
            }
        });
    } else {
        console.log('📱 Service Worker skipped - use HTTPS for PWA features');
    }
}

// Show update notification
function showUpdateNotification() {
    // Create update notification
    const updateBanner = document.createElement('div');
    updateBanner.className = 'update-banner';
    updateBanner.innerHTML = `
        <div class="update-content">
            <span>🔄 A new version of xDREAM Gaming is available!</span>
            <button class="update-btn" onclick="updateServiceWorker()">Update Now</button>
            <button class="dismiss-btn" onclick="dismissUpdate(this)">Later</button>
        </div>
    `;
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
        .update-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(45deg, #00ffff, #ff00ff);
            color: #000;
            padding: 1rem;
            z-index: 10002;
            animation: slideDown 0.3s ease-out;
            font-weight: bold;
        }
        
        .update-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .update-btn, .dismiss-btn {
            background: #000;
            color: #00ffff;
            border: 2px solid #000;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .update-btn:hover {
            background: transparent;
            color: #000;
        }
        
        .dismiss-btn {
            background: transparent;
            color: #000;
            border-color: #000;
        }
        
        .dismiss-btn:hover {
            background: #000;
            color: #00ffff;
        }
        
        @keyframes slideDown {
            from {
                transform: translateY(-100%);
            }
            to {
                transform: translateY(0);
            }
        }
        
        @media (max-width: 768px) {
            .update-content {
                font-size: 0.875rem;
            }
            
            .update-btn, .dismiss-btn {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }
        }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(updateBanner);
}

// Update service worker
window.updateServiceWorker = function() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then(registration => {
            registration.waiting?.postMessage({ type: 'SKIP_WAITING' });
        });
    }
};

// Dismiss update notification
window.dismissUpdate = function(button) {
    const banner = button.closest('.update-banner');
    if (banner) {
        banner.style.animation = 'slideDown 0.3s ease-out reverse';
        setTimeout(() => {
            banner.remove();
        }, 300);
    }
};

// Install Prompt Handling
function handleInstallPrompt() {
    let deferredPrompt;
    const installPrompt = document.getElementById('installPrompt');
    const installBtn = document.getElementById('installBtn');
    const closePrompt = document.getElementById('closePrompt');

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        
        // Show install prompt after 3 seconds
        setTimeout(() => {
            if (installPrompt) {
                installPrompt.classList.add('show');
            }
        }, 3000);
    });

    if (installBtn) {
        installBtn.addEventListener('click', () => {
            if (installPrompt) {
                installPrompt.classList.remove('show');
            }
            
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    console.log('User choice:', choiceResult.outcome);
                    deferredPrompt = null;
                });
            }
        });
    }

    if (closePrompt) {
        closePrompt.addEventListener('click', () => {
            if (installPrompt) {
                installPrompt.classList.remove('show');
            }
        });
    }
}

// Online/Offline Status
function handleOnlineStatus() {
    const offlineIndicator = document.getElementById('offlineIndicator');

    function updateOnlineStatus() {
        if (!offlineIndicator) return;
        
        if (navigator.onLine) {
            offlineIndicator.classList.remove('show');
        } else {
            offlineIndicator.classList.add('show');
        }
    }

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    updateOnlineStatus();
}

// Notification Permission
function requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
        setTimeout(() => {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    showNotification('🎮 xDREAM Gaming', 'Welcome to the future of gaming communities!');
                }
            });
        }, 5000);
    }
}

// Show Notification
function showNotification(title, body) {
    if ('Notification' in window && Notification.permission === 'granted') {
        const notification = new Notification(title, {
            body: body,
            icon: '/icons/icon-192x192.png',
            badge: '/icons/icon-72x72.png',
            vibrate: [200, 100, 200]
        });
        
        setTimeout(() => notification.close(), 5000);
    }
}

// Setup cache control for development
function setupCacheControl() {
    // Add cache clear button for development
    if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
        const clearCacheBtn = document.createElement('button');
        clearCacheBtn.textContent = '🗑️ Clear Cache';
        clearCacheBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #ff0000;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            z-index: 10000;
            font-weight: bold;
        `;
        
        clearCacheBtn.addEventListener('click', async () => {
            if ('serviceWorker' in navigator) {
                const registration = await navigator.serviceWorker.ready;
                registration.active?.postMessage({ type: 'CLEAR_CACHE' });
                
                // Also clear browser caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }
                
                alert('Cache cleared! Reloading page...');
                window.location.reload(true);
            }
        });
        
        document.body.appendChild(clearCacheBtn);
    }
}

// Force reload with cache bypass (for development)
window.forceReload = function() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.ready.then(registration => {
            registration.unregister().then(() => {
                if ('caches' in window) {
                    caches.keys().then(names => {
                        Promise.all(names.map(name => caches.delete(name)));
                    });
                }
                window.location.reload(true);
            });
        });
    } else {
        window.location.reload(true);
    }
};