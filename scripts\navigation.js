// scripts/navigation.js - Navigation functionality with critical fixes

function initializeNavigation() {
    // Set up navigation event listeners
    setupNavigationListeners();
    
    // Set up keyboard navigation
    setupKeyboardNavigation();
    
    // Set up touch/swipe navigation
    setupTouchNavigation();
    
    // CRITICAL FIX: Remove scroll navigation setup - only use for content scrolling
    // setupScrollNavigation(); // DISABLED - scroll should only scroll content, not navigate
    
    // Custom cursor - only if supported
    if (window.matchMedia('(hover: hover)').matches) {
        setupCustomCursor();
    }
    
    // ACCESSIBILITY FIX: Set up ARIA attributes
    setupAccessibility();
}

// Navigate to specific dimension
function navigateToDimension(dimension) {
    const state = window.xDreamState;
    
    if (dimension < 0 || dimension >= state.totalDimensions) return;
    
    // PERFORMANCE FIX: Debounce rapid navigation
    if (state.isNavigating) return;
    state.isNavigating = true;
    
    state.currentDimension = dimension;
    const universe = document.getElementById('universe');
    
    // CRITICAL FIX: Check if universe exists
    if (!universe) {
        console.error('Universe container not found');
        state.isNavigating = false;
        return;
    }
    
    // MOBILE FIX: Use 3D transform for better performance
    universe.style.transform = `translate3d(-${dimension * 100}vw, 0, 0)`;
    
    // Update navigation orbs
    document.querySelectorAll('.nav-orb').forEach((orb, index) => {
        orb.classList.toggle('active', index === dimension);
        // ACCESSIBILITY FIX: Update ARIA attributes
        orb.setAttribute('aria-current', index === dimension ? 'true' : 'false');
    });
    
    // Update dimension indicators
    document.querySelectorAll('.indicator-dot').forEach((dot, index) => {
        dot.classList.toggle('active', index === dimension);
        // ACCESSIBILITY FIX: Update ARIA attributes
        dot.setAttribute('aria-selected', index === dimension ? 'true' : 'false');
    });
    
    // Update page title for better UX
    const titles = ['xDREAM Gaming', 'Palworld Servers', '7 Days to Die', 'Project Zomboid', 'Dune: Awakening', 'Discord Community'];
    document.title = titles[dimension] || 'xDREAM Gaming';
    
    // ACCESSIBILITY FIX: Announce dimension change
    announceChange(`Navigated to ${titles[dimension]}`);
    
    // Reset navigation flag after transition
    setTimeout(() => {
        state.isNavigating = false;
    }, 500);
}

// Set up navigation listeners with better error handling
function setupNavigationListeners() {
    // Navigation orbs, indicators, and game portals
    document.querySelectorAll('.nav-orb, .indicator-dot, .game-portal').forEach(element => {
        // ACCESSIBILITY FIX: Add keyboard support
        element.addEventListener('click', handleNavigation);
        element.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleNavigation.call(element, e);
            }
        });
    });
}

function handleNavigation(e) {
    e.preventDefault();
    const dimension = parseInt(this.dataset.dimension);
    if (!isNaN(dimension)) {
        navigateToDimension(dimension);
    }
}

// Keyboard navigation with better key handling
function setupKeyboardNavigation() {
    let keydownTimeout;
    
    document.addEventListener('keydown', (e) => {
        const state = window.xDreamState;
        
        // Prevent navigation if user is typing in an input
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }
        
        // PERFORMANCE FIX: Debounce rapid key presses
        clearTimeout(keydownTimeout);
        
        switch(e.key) {
            case 'ArrowRight':
            case 'ArrowDown':
                e.preventDefault();
                keydownTimeout = setTimeout(() => {
                    if (state.currentDimension < state.totalDimensions - 1) {
                        navigateToDimension(state.currentDimension + 1);
                    }
                }, 50);
                break;
            case 'ArrowLeft':
            case 'ArrowUp':
                e.preventDefault();
                keydownTimeout = setTimeout(() => {
                    if (state.currentDimension > 0) {
                        navigateToDimension(state.currentDimension - 1);
                    }
                }, 50);
                break;
            case 'Home':
                e.preventDefault();
                navigateToDimension(0);
                break;
            case 'End':
                e.preventDefault();
                navigateToDimension(state.totalDimensions - 1);
                break;
            case ' ': // Spacebar for auto-advance
                e.preventDefault();
                toggleAutoAdvance();
                break;
            // Number keys for direct navigation
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
                e.preventDefault();
                const num = parseInt(e.key) - 1;
                if (num < state.totalDimensions) {
                    navigateToDimension(num);
                }
                break;
        }
    });
}

// CRITICAL FIX: Enhanced touch/swipe navigation with better detection
function setupTouchNavigation() {
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchEndY = 0;
    let touchStartTime = 0;
    let isScrollingContent = false;
    let startElement = null;

    // MOBILE FIX: Better touch detection
    document.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        touchStartY = e.changedTouches[0].screenY;
        touchStartTime = Date.now();
        isScrollingContent = false;
        startElement = e.target;
        
        // CRITICAL FIX: More comprehensive scrollable area detection
        const scrollableParent = e.target.closest(
            '.game-dimension, .dimension-content, .all-passives-container, ' +
            '.server-settings-container, .custom-passives-section, .zomboid-setup-section, ' +
            '.economy-section, .community-section, .dune-overview-section, .connection-section, ' +
            '.server-settings-section, .world-structure-section, .community-rules-section, ' +
            '.join-community-section, .discord-overview-section, .discord-features-section, ' +
            '.discord-commands-section, .discord-stats-section, .join-discord-section, ' +
            '.settings-grid, .passives-grid, .features-grid, .commands-grid, .stats-grid'
        );
        
        if (scrollableParent) {
            // Check if the element actually has scrollable content
            const hasVerticalScroll = scrollableParent.scrollHeight > scrollableParent.clientHeight;
            isScrollingContent = hasVerticalScroll;
        } else {
            isScrollingContent = false;
        }
    }, { passive: true });

    document.addEventListener('touchmove', (e) => {
        // CRITICAL FIX: Always allow vertical scrolling in scrollable content
        if (isScrollingContent) {
            return; // Allow normal scrolling
        }
        
        // CRITICAL FIX: Check if we're in home dimension - prevent all scrolling there
        const homeElement = startElement?.closest('.home-dimension');
        if (homeElement) {
            e.preventDefault();
            return;
        }
        
        // CRITICAL FIX: For other cases, detect swipe direction
        const currentX = e.changedTouches[0].screenX;
        const currentY = e.changedTouches[0].screenY;
        const deltaX = Math.abs(currentX - touchStartX);
        const deltaY = Math.abs(currentY - touchStartY);
        
        // If horizontal movement is significantly more than vertical, prevent default
        if (deltaX > deltaY * 2 && deltaX > 30) {
            e.preventDefault();
        }
    }, { passive: false });

    document.addEventListener('touchend', (e) => {
        if (isScrollingContent) return;
        
        touchEndX = e.changedTouches[0].screenX;
        touchEndY = e.changedTouches[0].screenY;
        
        // Only process if touch was quick (not a scroll)
        const touchDuration = Date.now() - touchStartTime;
        if (touchDuration < 500) {
            handleSwipe();
        }
    }, { passive: true });

    function handleSwipe() {
        const swipeThreshold = 75;
        const diffX = touchStartX - touchEndX;
        const diffY = Math.abs(touchStartY - touchEndY);
        const state = window.xDreamState;
        
        // Only handle horizontal swipes that are significantly horizontal
        if (Math.abs(diffX) > swipeThreshold && Math.abs(diffX) > diffY * 1.5) {
            if (diffX > 0 && state.currentDimension < state.totalDimensions - 1) {
                // Swipe left -> go right
                navigateToDimension(state.currentDimension + 1);
            } else if (diffX < 0 && state.currentDimension > 0) {
                // Swipe right -> go left
                navigateToDimension(state.currentDimension - 1);
            }
        }
    }
}

// CRITICAL FIX: REMOVED scroll navigation - scroll should only scroll content
// This function is intentionally removed/disabled to fix the scroll behavior issue
/*
function setupScrollNavigation() {
    // DISABLED: Scroll wheel should not navigate between dimensions
    // Only keyboard arrows and touch swipes should navigate
    // Scroll wheel should only be used for scrolling content within dimensions
}
*/

// CRITICAL FIX: New function to ensure content scrolling works properly
function ensureContentScrolling() {
    // Make sure all scrollable content areas work properly with scroll wheel
    const scrollableAreas = document.querySelectorAll(
        '.game-dimension, .dimension-content, .all-passives-container, ' +
        '.server-settings-container, .custom-passives-section, .zomboid-setup-section, ' +
        '.economy-section, .community-section, .dune-overview-section, .connection-section, ' +
        '.server-settings-section, .world-structure-section, .community-rules-section, ' +
        '.join-community-section, .discord-overview-section, .discord-features-section, ' +
        '.discord-commands-section, .discord-stats-section, .join-discord-section, ' +
        '.setup-guide-section, .quick-links-section'
    );

    scrollableAreas.forEach(area => {
        // Ensure proper scroll behavior
        area.style.overflowY = 'auto';
        area.style.overflowX = 'hidden';
        area.style.webkitOverflowScrolling = 'touch';

        // CRITICAL FIX: Prevent any scroll events from bubbling up that could trigger navigation
        area.addEventListener('wheel', (e) => {
            // Stop propagation to prevent any parent handlers from triggering navigation
            e.stopPropagation();

            // Check if we can scroll in the requested direction
            const canScrollUp = area.scrollTop > 0;
            const canScrollDown = area.scrollTop < (area.scrollHeight - area.clientHeight - 1);

            // If we can't scroll in the requested direction, prevent the event
            if ((e.deltaY < 0 && !canScrollUp) || (e.deltaY > 0 && !canScrollDown)) {
                e.preventDefault();
                return false;
            }
        }, { passive: false });

        // Add scroll event listener for debugging
        area.addEventListener('scroll', (e) => {
            // This is just for the scroll to work naturally within content
            // No dimension navigation here - just stop propagation to be safe
            e.stopPropagation();
        }, { passive: true });
    });
}

// Custom cursor with performance optimizations
function setupCustomCursor() {
    const cursor = document.querySelector('.cursor');
    if (!cursor) return;
    
    let mouseX = 0, mouseY = 0;
    let cursorX = 0, cursorY = 0;
    let isMoving = false;
    let rafId = null;

    // PERFORMANCE FIX: Throttled mouse tracking
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
        
        if (!isMoving) {
            isMoving = true;
            rafId = requestAnimationFrame(updateCursor);
        }
    });

    function updateCursor() {
        // Smooth interpolation for better visual effect
        const speed = 0.15;
        cursorX += (mouseX - cursorX) * speed;
        cursorY += (mouseY - cursorY) * speed;
        
        cursor.style.transform = `translate(${cursorX}px, ${cursorY}px)`;
        
        // Continue animation if mouse is still moving
        if (Math.abs(mouseX - cursorX) > 0.1 || Math.abs(mouseY - cursorY) > 0.1) {
            rafId = requestAnimationFrame(updateCursor);
        } else {
            isMoving = false;
        }
    }

    // Cursor interactions
    document.addEventListener('mousedown', () => cursor.classList.add('active'));
    document.addEventListener('mouseup', () => cursor.classList.remove('active'));
    
    // Hide cursor when leaving window
    document.addEventListener('mouseleave', () => cursor.style.opacity = '0');
    document.addEventListener('mouseenter', () => cursor.style.opacity = '1');
    
    // Cleanup function
    cursor.cleanupFunction = () => {
        if (rafId) cancelAnimationFrame(rafId);
    };
}

// Auto-advance functionality with better state management
function toggleAutoAdvance() {
    const state = window.xDreamState;
    state.autoAdvance = !state.autoAdvance;
    
    if (state.autoAdvance) {
        // Show visual indicator
        console.log('🔄 Auto-advance enabled');
        announceChange('Auto-advance enabled');
        
        state.autoAdvanceInterval = setInterval(() => {
            // Only advance if page is visible
            if (window.xDreamState.isPageVisible) {
                navigateToDimension((state.currentDimension + 1) % state.totalDimensions);
            }
        }, 5000);
    } else {
        console.log('⏸️ Auto-advance disabled');
        announceChange('Auto-advance disabled');
        
        clearInterval(state.autoAdvanceInterval);
        state.autoAdvanceInterval = null;
    }
}

// ACCESSIBILITY FIX: Set up ARIA live region for announcements
function setupAccessibility() {
    // Create live region if it doesn't exist
    if (!document.getElementById('navigation-announcer')) {
        const announcer = document.createElement('div');
        announcer.id = 'navigation-announcer';
        announcer.setAttribute('role', 'status');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
        document.body.appendChild(announcer);
    }
    
    // CRITICAL FIX: Set up content scrolling after DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', ensureContentScrolling);
    } else {
        ensureContentScrolling();
    }
}

// ACCESSIBILITY FIX: Announce changes for screen readers
function announceChange(message) {
    const announcer = document.getElementById('navigation-announcer');
    if (announcer) {
        announcer.textContent = message;
        // Clear after announcement
        setTimeout(() => {
            announcer.textContent = '';
        }, 1000);
    }
}

// CRITICAL FIX: Add scroll lock/unlock functions for better control
function lockScroll() {
    document.body.style.overflow = 'hidden';
}

function unlockScroll() {
    document.body.style.overflow = '';
}

// CRITICAL FIX: Handle scroll behavior on dimension change
function handleDimensionChange(newDimension) {
    const currentDimension = document.querySelector('.dimension.active') || 
                           document.querySelector('.dimension[data-dimension="' + newDimension + '"]');
    
    if (currentDimension) {
        // Reset scroll position when changing dimensions
        const scrollableContent = currentDimension.querySelector('.dimension-content');
        if (scrollableContent) {
            scrollableContent.scrollTop = 0;
        }
    }
}

// CRITICAL FIX: Global scroll prevention for dimension navigation
function preventScrollNavigation() {
    // Add a global wheel event listener to catch any scroll events that might trigger navigation
    document.addEventListener('wheel', (e) => {
        // If the event target is not within a scrollable content area, prevent it
        const isInScrollableArea = e.target.closest(
            '.game-dimension, .dimension-content, .all-passives-container, ' +
            '.server-settings-container, .custom-passives-section, .zomboid-setup-section, ' +
            '.economy-section, .community-section, .dune-overview-section, .connection-section, ' +
            '.server-settings-section, .world-structure-section, .community-rules-section, ' +
            '.join-community-section, .discord-overview-section, .discord-features-section, ' +
            '.discord-commands-section, .discord-stats-section, .join-discord-section, ' +
            '.setup-guide-section, .quick-links-section'
        );

        if (!isInScrollableArea) {
            // Prevent any scroll events outside of content areas
            e.preventDefault();
            e.stopPropagation();
            return false;
        }
    }, { passive: false, capture: true }); // Use capture to catch events early
}

// CRITICAL FIX: Initialize scroll behavior fixes when script loads
document.addEventListener('DOMContentLoaded', () => {
    // Ensure content scrolling is set up
    ensureContentScrolling();

    // Add global scroll prevention
    preventScrollNavigation();

    // Add message for debugging
    console.log('🖱️ Scroll navigation disabled - use arrow keys or swipe to navigate dimensions');
    console.log('📜 Scroll wheel will only scroll content within dimensions');
    console.log('🛡️ Global scroll prevention active - no accidental navigation');
});