// Enhanced Site Builder Core - Complete Data Management and Site Generation
class SiteBuilderCore {
    constructor() {
        this.pages = [];
        this.currentPageId = null;
        this.templates = {};
        this.storageKey = 'xdream-site-builder-data';
        this.backupKey = 'xdream-site-builder-backup';
        this.siteConfig = {
            title: 'xDREAM Gaming',
            description: 'Enter the Gaming Multiverse',
            version: '2.0.0'
        };
        
        this.init();
    }

    async init() {
        await this.loadData();
        await this.loadTemplates();
        this.setupEventListeners();
        this.setupAutoSave();
        console.log('🚀 Enhanced Site Builder Core initialized');
    }

    // Enhanced Data Management
    async loadData() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const data = JSON.parse(stored);
                this.pages = data.pages || [];
                this.siteConfig = { ...this.siteConfig, ...(data.siteConfig || {}) };
                console.log(`📄 Loaded ${this.pages.length} pages from storage`);
                
                // Validate and fix page order
                this.validatePageOrder();
            } else {
                await this.importExistingPages();
            }
        } catch (error) {
            console.error('❌ Error loading data:', error);
            this.showMessage('Error loading site data. Starting with default pages.', 'warning');
            await this.importExistingPages();
        }
    }

    validatePageOrder() {
        // Ensure all pages have valid order numbers
        let needsReorder = false;
        this.pages.forEach((page, index) => {
            if (typeof page.order !== 'number' || page.order !== index) {
                page.order = index;
                needsReorder = true;
            }
        });
        
        if (needsReorder) {
            this.pages.sort((a, b) => a.order - b.order);
            console.log('🔧 Fixed page ordering');
        }
    }

    async saveData() {
        try {
            const data = {
                pages: this.pages,
                siteConfig: this.siteConfig,
                lastModified: new Date().toISOString(),
                version: '2.0.0'
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            console.log('💾 Data saved successfully');
            this.showMessage('Changes saved successfully', 'success');
            
            // Auto-backup every 10 saves
            const saveCount = parseInt(localStorage.getItem('xdream-save-count') || '0') + 1;
            localStorage.setItem('xdream-save-count', saveCount.toString());
            
            if (saveCount % 10 === 0) {
                await this.createAutoBackup();
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error saving data:', error);
            this.showMessage('Error saving data. Please try again.', 'error');
            return false;
        }
    }

    async createAutoBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                pages: this.pages,
                siteConfig: this.siteConfig,
                timestamp: timestamp,
                version: '2.0.0',
                type: 'auto-backup'
            };
            
            localStorage.setItem(`${this.backupKey}-auto-${timestamp}`, JSON.stringify(backupData));
            console.log('📦 Auto-backup created');
            
            // Clean old auto-backups (keep only last 5)
            this.cleanOldBackups();
            
        } catch (error) {
            console.error('❌ Error creating auto-backup:', error);
        }
    }

    cleanOldBackups() {
        try {
            const backupKeys = Object.keys(localStorage)
                .filter(key => key.startsWith(`${this.backupKey}-auto-`))
                .sort()
                .reverse();
            
            // Remove all but the last 5 backups
            backupKeys.slice(5).forEach(key => {
                localStorage.removeItem(key);
            });
            
            if (backupKeys.length > 5) {
                console.log(`🧹 Cleaned ${backupKeys.length - 5} old backups`);
            }
        } catch (error) {
            console.error('❌ Error cleaning old backups:', error);
        }
    }

    // Enhanced Page Management
    createPage(pageData) {
        const newPage = {
            id: this.generateId(),
            title: this.sanitizeInput(pageData.title) || 'New Page',
            subtitle: this.sanitizeInput(pageData.subtitle) || '',
            icon: this.validateIcon(pageData.icon) || '🎮',
            accentColor: this.validateColor(pageData.accentColor) || '#00ffff',
            template: pageData.template || 'custom',
            backgroundEffects: {
                particles: pageData.particles || false,
                matrix: pageData.matrix !== false, // Default to true
                glitch: pageData.glitch || false
            },
            content: this.getTemplateContent(pageData.template),
            order: this.pages.length,
            created: new Date().toISOString(),
            modified: new Date().toISOString(),
            isDefault: false,
            seo: {
                description: pageData.description || '',
                keywords: pageData.keywords || ''
            }
        };

        this.pages.push(newPage);
        this.saveData();
        console.log(`➕ Created new page: ${newPage.title}`);
        this.showMessage(`Page "${newPage.title}" created successfully`, 'success');
        return newPage;
    }

    updatePage(pageId, updates) {
        const pageIndex = this.pages.findIndex(p => p.id === pageId);
        if (pageIndex === -1) {
            console.error(`❌ Page not found: ${pageId}`);
            this.showMessage('Page not found', 'error');
            return false;
        }

        // Sanitize and validate updates
        const sanitizedUpdates = this.sanitizePageUpdates(updates);
        
        this.pages[pageIndex] = {
            ...this.pages[pageIndex],
            ...sanitizedUpdates,
            modified: new Date().toISOString()
        };

        this.saveData();
        console.log(`✏️ Updated page: ${this.pages[pageIndex].title}`);
        return this.pages[pageIndex];
    }

    sanitizePageUpdates(updates) {
        const sanitized = {};
        
        if (updates.title) sanitized.title = this.sanitizeInput(updates.title);
        if (updates.subtitle !== undefined) sanitized.subtitle = this.sanitizeInput(updates.subtitle);
        if (updates.icon) sanitized.icon = this.validateIcon(updates.icon);
        if (updates.accentColor) sanitized.accentColor = this.validateColor(updates.accentColor);
        if (updates.content) sanitized.content = this.sanitizeContent(updates.content);
        if (updates.backgroundEffects) sanitized.backgroundEffects = updates.backgroundEffects;
        if (updates.seo) sanitized.seo = updates.seo;
        
        return sanitized;
    }

    deletePage(pageId) {
        const pageIndex = this.pages.findIndex(p => p.id === pageId);
        if (pageIndex === -1) {
            console.error(`❌ Page not found: ${pageId}`);
            this.showMessage('Page not found', 'error');
            return false;
        }

        const page = this.pages[pageIndex];
        if (page.isDefault) {
            this.showMessage('Cannot delete default pages', 'error');
            return false;
        }

        this.pages.splice(pageIndex, 1);
        this.reorderPages();
        this.saveData();
        console.log(`🗑️ Deleted page: ${page.title}`);
        this.showMessage(`Page "${page.title}" deleted successfully`, 'success');
        return true;
    }

    duplicatePage(pageId) {
        const originalPage = this.getPage(pageId);
        if (!originalPage) {
            this.showMessage('Page not found', 'error');
            return false;
        }

        const duplicatedPage = {
            ...originalPage,
            id: this.generateId(),
            title: `${originalPage.title} (Copy)`,
            order: this.pages.length,
            created: new Date().toISOString(),
            modified: new Date().toISOString(),
            isDefault: false
        };

        this.pages.push(duplicatedPage);
        this.saveData();
        console.log(`📋 Duplicated page: ${originalPage.title}`);
        this.showMessage(`Page duplicated successfully`, 'success');
        return duplicatedPage;
    }

    reorderPage(pageId, newOrder) {
        const pageIndex = this.pages.findIndex(p => p.id === pageId);
        if (pageIndex === -1) return false;

        const page = this.pages[pageIndex];
        const oldOrder = page.order;
        
        // Remove from current position
        this.pages.splice(pageIndex, 1);
        
        // Insert at new position
        this.pages.splice(newOrder, 0, page);
        
        // Update all order values
        this.reorderPages();
        this.saveData();
        
        console.log(`🔄 Moved page "${page.title}" from position ${oldOrder} to ${newOrder}`);
        this.showMessage('Page order updated', 'success');
        return true;
    }

    // Enhanced Site Generation
    generateFullSite() {
        try {
            console.log('🏗️ Starting site generation...');
            
            const siteData = this.prepareSiteData();
            const indexHTML = this.generateIndexHTML(siteData);
            const manifestJSON = this.generateManifest(siteData);
            const navigationJS = this.generateNavigationJS(siteData);
            
            const result = {
                success: true,
                files: {
                    'index.html': indexHTML,
                    'manifest.json': manifestJSON,
                    'scripts/generated-navigation.js': navigationJS
                },
                metadata: {
                    pages: this.pages.length,
                    timestamp: new Date().toISOString(),
                    version: this.siteConfig.version
                }
            };

            console.log('✅ Site generation completed successfully');
            this.showMessage('Site generated successfully!', 'success');
            return result;
            
        } catch (error) {
            console.error('❌ Site generation failed:', error);
            this.showMessage('Site generation failed. Please check your content.', 'error');
            return { success: false, error: error.message };
        }
    }

    prepareSiteData() {
        return {
            title: this.siteConfig.title,
            description: this.siteConfig.description,
            pages: this.pages.map(page => ({
                id: page.id,
                title: page.title,
                subtitle: page.subtitle,
                icon: page.icon,
                accentColor: page.accentColor,
                order: page.order,
                backgroundEffects: page.backgroundEffects,
                seo: page.seo
            })),
            totalDimensions: this.pages.length,
            generated: new Date().toISOString(),
            version: this.siteConfig.version
        };
    }

    generateIndexHTML(siteData) {
        const pages = this.getAllPages();
        
        // Generate navigation orbs
        const navOrbs = pages.map((page, index) => 
            `<div class="nav-orb${index === 0 ? ' active' : ''}" data-dimension="${index}" title="${this.escapeHtml(page.title)}" role="button" tabindex="0" aria-label="Navigate to ${this.escapeHtml(page.title)}"${index === 0 ? ' aria-current="true"' : ''}>${page.icon}</div>`
        ).join('\n        ');

        // Generate dimension indicators
        const indicators = pages.map((page, index) => 
            `<div class="indicator-dot${index === 0 ? ' active' : ''}" data-dimension="${index}" role="tab" tabindex="0" aria-selected="${index === 0 ? 'true' : 'false'}" aria-label="${this.escapeHtml(page.title)} dimension"></div>`
        ).join('\n        ');

        // Generate page sections
        const sections = pages.map((page, index) => {
            const content = this.generatePageContent(page);
            const className = index === 0 ? 'dimension home-dimension' : `dimension game-dimension ${page.id}-dimension`;
            const styleVars = page.accentColor ? `style="--accent-color: ${page.accentColor}"` : '';
            
            return `
        <!-- ${this.escapeHtml(page.title)} Dimension -->
        <section class="${className}" role="tabpanel" aria-label="${this.escapeHtml(page.title)}" ${styleVars}>
            ${this.generateBackgroundEffects(page.backgroundEffects, page.id)}
            <div class="dimension-content">
                ${content}
            </div>
        </section>`;
        }).join('\n');

        // Generate CSS variables for accent colors
        const cssVariables = pages.map(page => {
            const safeName = page.id.replace(/[^a-zA-Z0-9-_]/g, '');
            return `--${safeName}-accent: ${page.accentColor};`;
        }).join('\n    ');

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>${this.escapeHtml(siteData.title)} - Enter the Future</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="${this.escapeHtml(siteData.description)}">
    <meta name="keywords" content="gaming, community, servers, multiplayer">
    <meta name="author" content="${this.escapeHtml(siteData.title)}">

    <!-- SECURITY FIX: Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://discord.xdreamserver.com https://store.xdreamserver.com; font-src 'self'; manifest-src 'self';">

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="${this.escapeHtml(siteData.title)}">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="${this.escapeHtml(siteData.title)}">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#00ffff">
    <meta name="msapplication-TileColor" content="#00ffff">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjMDAwIi8+Cjx0ZXh0IHg9IjE2IiB5PSIyMCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzAwZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+WEQ8L3RleHQ+Cjwvc3ZnPgo=">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://discord.xdreamserver.com">
    <link rel="preconnect" href="https://store.xdreamserver.com">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/dimensions.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- Generated CSS Variables -->
    <style>
        :root {
            ${cssVariables}
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen" role="status" aria-live="polite">
        <div class="loading-spinner"></div>
        <div class="loading-text">INITIALIZING QUANTUM MATRIX...</div>
    </div>

    <!-- Offline Indicator -->
    <div class="offline-indicator" id="offlineIndicator" role="alert" aria-live="assertive">
        🔌 You're offline - Some features may be limited
    </div>

    <!-- Install Prompt -->
    <div class="install-prompt" id="installPrompt" role="dialog" aria-label="Install app prompt">
        <span>📱 Install ${this.escapeHtml(siteData.title)} App for the ultimate experience!</span>
        <div>
            <button class="install-btn" id="installBtn" aria-label="Install app">Install</button>
            <button class="close-btn" id="closePrompt" aria-label="Close install prompt">×</button>
        </div>
    </div>
    
    <div class="cursor" aria-hidden="true"></div>

    <!-- Matrix Background -->
    <canvas class="matrix-bg" id="matrix" aria-hidden="true"></canvas>

    <!-- Navigation Portal -->
    <nav class="nav-portal" role="navigation" aria-label="Game dimensions navigation">
        ${navOrbs}
    </nav>

    <!-- Dimension Indicator -->
    <div class="dimension-indicator" role="tablist" aria-label="Dimension indicators">
        ${indicators}
    </div>

    <!-- Main Universe Container -->
    <main class="universe" id="universe" role="main">
${sections}
    </main>

    <!-- Site Builder Access (only show if on localhost/development) -->
    <div class="site-builder-access" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000; display: none;" id="siteBuilderAccess">
        <button class="quantum-button" onclick="window.location.href='site-builder/index.html'" title="Open Site Builder">
            🛠️ EDIT SITE
        </button>
    </div>

    <!-- JavaScript Files -->
    <script src="scripts/utils.js"></script>
    <script src="scripts/pwa.js"></script>
    <script src="scripts/navigation.js"></script>
    <script src="scripts/effects.js"></script>
    <script src="scripts/main.js"></script>
    
    <!-- Generated site data -->
    <script>
        window.xDreamSiteData = ${JSON.stringify(siteData, null, 8)};
        
        // Show site builder access in development
        if (location.hostname === 'localhost' || location.hostname === '127.0.0.1' || location.hostname === '') {
            document.getElementById('siteBuilderAccess').style.display = 'block';
        }
    </script>
</body>
</html>`;
    }

    generatePageContent(page) {
        // Always include header
        let content = `
                <div class="dimension-header">
                    <h2 class="dimension-title">${this.escapeHtml(page.title)}</h2>
                    ${page.subtitle ? `<div class="dimension-subtitle">${this.escapeHtml(page.subtitle)}</div>` : ''}
                </div>`;

        if (!page.content || page.content.length === 0) {
            return content;
        }

        const contentBlocks = page.content.map(block => {
            return this.generateContentBlock(block, page);
        }).filter(block => block).join('\n                ');

        return content + '\n                ' + contentBlocks;
    }

    generateContentBlock(block, page) {
        switch (block.type) {
            case 'text':
            case 'custom':
                return block.content || '';
            
            case 'server-node':
                const safePageId = page.id.replace(/[^a-zA-Z0-9-_]/g, '');
                return `
                <div class="server-matrix">
                    <div class="server-node ${safePageId}-node">
                        <div class="server-name">${this.escapeHtml(block.serverName || 'Server Name')}</div>
                        <button class="server-address" onclick="copyToClipboard('${this.escapeHtml(block.serverAddress || '')}')" aria-label="Copy server address ${this.escapeHtml(block.serverAddress || '')}">
                            ${this.escapeHtml(block.serverAddress || 'server.address.com:port')}
                        </button>
                        <div class="server-info">${this.escapeHtml(block.serverInfo || 'Server Info')}</div>
                        ${this.generatePlayerCount(block, page)}
                    </div>
                </div>`;
            
            case 'button':
                const buttonClass = this.sanitizeClassName(block.style || 'quantum-button');
                const buttonUrl = this.sanitizeUrl(block.url || '#');
                return `
                <div class="button-group">
                    <button class="${buttonClass}" onclick="window.open('${buttonUrl}', '_blank')" aria-label="${this.escapeHtml(block.text || 'Button')}">
                        ${this.escapeHtml(block.text || 'Button')}
                    </button>
                </div>`;
            
            case 'image':
                return `
                <div class="image-block">
                    <img src="${this.sanitizeUrl(block.src || '')}" alt="${this.escapeHtml(block.alt || '')}" class="content-image">
                    ${block.caption ? `<div class="image-caption">${this.escapeHtml(block.caption)}</div>` : ''}
                </div>`;
            
            default:
                console.warn(`Unknown block type: ${block.type}`);
                return '';
        }
    }

    generatePlayerCount(block, page) {
        if (!block.showPlayerCount) return '';
        
        return `
                        <div class="player-count" id="${page.id}-players" role="status" aria-live="polite">
                            <span class="player-icon" aria-hidden="true">👥</span>
                            <span class="player-text">Checking players...</span>
                        </div>`;
    }

    generateBackgroundEffects(effects, pageId) {
        if (!effects) return '';
        
        let html = '';
        
        if (effects.particles) {
            const safePageId = pageId.replace(/[^a-zA-Z0-9-_]/g, '');
            html += `
            <div class="${safePageId}-particles" id="${safePageId}Particles" aria-hidden="true"></div>`;
        }
        
        return html;
    }

    // Utility Functions
    generateId() {
        return 'page_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    sanitizeInput(input) {
        if (typeof input !== 'string') return '';
        return input.trim().replace(/[<>'"]/g, '');
    }

    validateIcon(icon) {
        if (typeof icon !== 'string' || icon.length === 0) return '🎮';
        // Allow emoji and basic characters, limit to 2 characters
        return icon.slice(0, 2);
    }

    validateColor(color) {
        if (typeof color !== 'string') return '#00ffff';
        // Basic hex color validation
        const hexRegex = /^#[0-9A-Fa-f]{6}$/;
        return hexRegex.test(color) ? color : '#00ffff';
    }

    sanitizeClassName(className) {
        if (typeof className !== 'string') return 'quantum-button';
        return className.replace(/[^a-zA-Z0-9\s-_]/g, '').trim();
    }

    sanitizeUrl(url) {
        if (typeof url !== 'string') return '#';
        // Basic URL sanitization
        try {
            if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('#')) {
                return url;
            }
            return '#';
        } catch {
            return '#';
        }
    }

    escapeHtml(text) {
        if (typeof text !== 'string') return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    sanitizeContent(content) {
        if (!Array.isArray(content)) return [];
        return content.map(block => {
            const sanitized = { ...block };
            if (sanitized.content && typeof sanitized.content === 'string') {
                // Allow basic HTML but escape dangerous content
                sanitized.content = sanitized.content
                    .replace(/<script[^>]*>.*?<\/script>/gi, '')
                    .replace(/javascript:/gi, '')
                    .replace(/on\w+\s*=/gi, '');
            }
            return sanitized;
        });
    }

    // Enhanced Templates and Import
    async importExistingPages() {
        const existingPages = [
            {
                id: 'home',
                title: 'xDREAM Gaming',
                subtitle: 'Enter the Gaming Multiverse',
                icon: '🏠',
                accentColor: '#00ffff',
                template: 'custom',
                backgroundEffects: { particles: false, matrix: true, glitch: true },
                content: [
                    {
                        type: 'text',
                        content: '<h1 class="logo-matrix glitch">xDREAM</h1><p class="tagline">Enter the Gaming Multiverse</p><div class="server-grid"><div class="game-portal" data-dimension="1" role="button" tabindex="0" aria-label="Go to Palworld servers"><div class="game-icon" aria-hidden="true">🦄</div><div class="game-title">PALWORLD</div></div><div class="game-portal" data-dimension="2" role="button" tabindex="0" aria-label="Go to 7 Days to Die servers"><div class="game-icon" aria-hidden="true">🧟</div><div class="game-title">7 DAYS TO DIE</div></div><div class="game-portal" data-dimension="3" role="button" tabindex="0" aria-label="Go to Project Zomboid servers"><div class="game-icon" aria-hidden="true">🧟‍♂️</div><div class="game-title">PROJECT ZOMBOID</div></div><div class="game-portal" data-dimension="4" role="button" tabindex="0" aria-label="Go to Dune: Awakening servers"><div class="game-icon" aria-hidden="true">🏜️</div><div class="game-title">DUNE: AWAKENING</div></div></div>'
                    },
                    {
                        type: 'button',
                        text: 'ENTER DISCORD PORTAL',
                        url: 'https://discord.xdreamserver.com',
                        style: 'quantum-button'
                    },
                    {
                        type: 'button',
                        text: 'ACCESS STORE MATRIX',
                        url: 'https://store.xdreamserver.com',
                        style: 'quantum-button'
                    }
                ],
                isDefault: true,
                order: 0,
                seo: { description: 'Enter the xDREAM Gaming multiverse - Your gateway to epic gaming adventures', keywords: 'gaming, community, servers, multiverse' }
            },
            // ... (other existing pages with similar structure)
        ];

        this.pages = existingPages;
        await this.saveData();
        console.log('📥 Imported existing pages');
    }

    // Auto-save functionality
    setupAutoSave() {
        setInterval(() => {
            if (this.pages.length > 0) {
                this.saveData();
            }
        }, 60000); // Auto-save every minute
    }

    // Enhanced event listeners and message system
    setupEventListeners() {
        const saveAllBtn = document.getElementById('saveAllBtn');
        if (saveAllBtn) {
            saveAllBtn.addEventListener('click', () => this.saveData());
        }

        const backupBtn = document.getElementById('backupBtn');
        if (backupBtn) {
            backupBtn.addEventListener('click', () => this.createManualBackup());
        }

        // Generate Site button
        const generateSiteBtn = document.getElementById('generateSiteBtn');
        if (generateSiteBtn) {
            generateSiteBtn.addEventListener('click', () => this.downloadGeneratedSite());
        }

        console.log('🎧 Enhanced event listeners setup complete');
    }

    async createManualBackup() {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                pages: this.pages,
                siteConfig: this.siteConfig,
                timestamp: timestamp,
                version: '2.0.0',
                type: 'manual-backup'
            };
            
            const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `xdream-backup-${timestamp}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            // Also save to localStorage
            localStorage.setItem(`${this.backupKey}-manual-${timestamp}`, JSON.stringify(backupData));
            
            console.log('📦 Manual backup created successfully');
            this.showMessage('Backup created and downloaded', 'success');
            return true;
        } catch (error) {
            console.error('❌ Error creating manual backup:', error);
            this.showMessage('Error creating backup', 'error');
            return false;
        }
    }

    downloadGeneratedSite() {
        const result = this.generateFullSite();
        if (!result.success) return;

        // Download the main HTML file
        const blob = new Blob([result.files['index.html']], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'index.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('📥 Generated site downloaded');
        this.showMessage('Site downloaded successfully! Upload to replace your existing index.html', 'success');
    }

    showMessage(message, type = 'info') {
        const messageContainer = document.getElementById('messageContainer');
        if (!messageContainer) {
            console.log(`[${type.toUpperCase()}] ${message}`);
            return;
        }

        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.innerHTML = `
            <div class="message-content">
                <span class="message-text">${this.escapeHtml(message)}</span>
                <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;

        messageContainer.appendChild(messageEl);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 5000);
    }

    // Public API
    getPage(pageId) {
        return this.pages.find(p => p.id === pageId);
    }

    getAllPages() {
        return [...this.pages].sort((a, b) => a.order - b.order);
    }

    reorderPages() {
        this.pages.forEach((page, index) => {
            page.order = index;
        });
    }

    getTemplateContent(templateName) {
        if (window.siteBuilderTemplates) {
            const template = window.siteBuilderTemplates.getTemplate(templateName);
            return template ? [...template.defaultContent] : [];
        }
        return [];
    }

    // Enhanced export/import
    async exportSite() {
        try {
            const siteData = {
                pages: this.pages,
                siteConfig: this.siteConfig,
                metadata: {
                    exported: new Date().toISOString(),
                    version: '2.0.0',
                    siteName: this.siteConfig.title
                }
            };

            const blob = new Blob([JSON.stringify(siteData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `xdream-site-export-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.showMessage('Site exported successfully', 'success');
            return true;
        } catch (error) {
            console.error('❌ Error exporting site:', error);
            this.showMessage('Error exporting site', 'error');
            return false;
        }
    }

    async importSite(file) {
        try {
            const text = await file.text();
            const data = JSON.parse(text);

            if (data.pages && Array.isArray(data.pages)) {
                // Validate imported data
                const validPages = data.pages.filter(page => 
                    page.id && page.title && typeof page.order === 'number'
                );

                if (validPages.length === 0) {
                    throw new Error('No valid pages found in import file');
                }

                this.pages = validPages;
                if (data.siteConfig) {
                    this.siteConfig = { ...this.siteConfig, ...data.siteConfig };
                }

                this.validatePageOrder();
                await this.saveData();
                
                this.showMessage(`Site imported successfully! ${validPages.length} pages loaded.`, 'success');
                return true;
            } else {
                throw new Error('Invalid file format - missing pages array');
            }
        } catch (error) {
            console.error('❌ Error importing site:', error);
            this.showMessage(`Error importing site: ${error.message}`, 'error');
            return false;
        }
    }
}

// Global instance
window.siteBuilderCore = new SiteBuilderCore();