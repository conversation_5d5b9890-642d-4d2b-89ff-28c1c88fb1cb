// Site Builder Main - Initialization and Coordination
class SiteBuilder {
    constructor() {
        this.initialized = false;
        this.version = '1.0.0';
        this.init();
    }

    async init() {
        try {
            console.log('🚀 Initializing xDREAM Site Builder...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
            } else {
                await this.initializeComponents();
            }
        } catch (error) {
            console.error('❌ Site Builder initialization failed:', error);
            this.showError('Failed to initialize Site Builder');
        }
    }

    async initializeComponents() {
        try {
            // Show loading screen
            this.showLoadingScreen();

            // Initialize matrix background effect
            this.initializeMatrixBackground();

            // Wait for core components to be ready
            await this.waitForComponents();

            // Initialize site generator
            this.initializeSiteGenerator();

            // Setup global event listeners
            this.setupGlobalEventListeners();

            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Hide loading screen
            this.hideLoadingScreen();

            this.initialized = true;
            console.log('✅ Site Builder fully initialized');
            
            // Show welcome message
            this.showWelcomeMessage();

        } catch (error) {
            console.error('❌ Component initialization failed:', error);
            this.showError('Failed to initialize components');
        }
    }

    async waitForComponents() {
        // Wait for core components to be available
        const maxWait = 10000; // 10 seconds
        const startTime = Date.now();

        while (!window.siteBuilderCore || !window.siteBuilderUI || !window.siteBuilderTemplates) {
            if (Date.now() - startTime > maxWait) {
                throw new Error('Components failed to load within timeout');
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        // Wait for core to finish loading data
        while (!window.siteBuilderCore.pages) {
            if (Date.now() - startTime > maxWait) {
                throw new Error('Core data failed to load within timeout');
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log('📦 All components loaded successfully');
    }

    initializeMatrixBackground() {
        // Initialize matrix background effect
        const canvas = document.getElementById('matrix');
        if (canvas && window.initializeMatrix) {
            try {
                window.initializeMatrix();
                console.log('🌌 Matrix background initialized');
            } catch (error) {
                console.warn('⚠️ Matrix background failed to initialize:', error);
            }
        }
    }

    initializeSiteGenerator() {
        this.siteGenerator = new SiteGenerator();
        console.log('🏗️ Site generator initialized');
    }

    setupGlobalEventListeners() {
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));

        // Handle beforeunload to save data
        window.addEventListener('beforeunload', (e) => {
            if (window.siteBuilderCore) {
                window.siteBuilderCore.saveData();
            }
        });

        // Handle visibility change for auto-save
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && window.siteBuilderCore) {
                window.siteBuilderCore.saveData();
            }
        });

        // Handle modal clicks
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                const modalId = e.target.id;
                if (modalId) {
                    closeModal(modalId);
                }
            }
        });

        console.log('🎧 Global event listeners setup');
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S: Save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (window.siteBuilderCore) {
                    window.siteBuilderCore.saveData();
                }
            }

            // Ctrl/Cmd + N: New page
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                if (window.siteBuilderUI) {
                    window.siteBuilderUI.showAddPageModal();
                }
            }

            // Ctrl/Cmd + P: Preview
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                if (window.siteBuilderUI && window.siteBuilderUI.currentPageId) {
                    window.siteBuilderUI.togglePreview();
                }
            }

            // Escape: Close modals/preview
            if (e.key === 'Escape') {
                // Close any open modals
                const activeModals = document.querySelectorAll('.modal-overlay.active');
                activeModals.forEach(modal => {
                    modal.classList.remove('active');
                });

                // Close preview if open
                const previewPanel = document.getElementById('previewPanel');
                if (previewPanel && previewPanel.classList.contains('active')) {
                    window.siteBuilderUI.togglePreview();
                }
            }
        });

        console.log('⌨️ Keyboard shortcuts setup');
    }

    handleResize() {
        // Handle responsive layout adjustments
        const sidebar = document.querySelector('.site-builder-sidebar');
        const editor = document.querySelector('.site-builder-editor');
        
        if (window.innerWidth <= 768) {
            // Mobile layout adjustments
            if (sidebar) sidebar.style.maxHeight = '300px';
        } else {
            // Desktop layout
            if (sidebar) sidebar.style.maxHeight = 'calc(100vh - 120px)';
        }
    }

    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 300);
            }, 500);
        }
    }

    showWelcomeMessage() {
        if (window.siteBuilderCore) {
            const pageCount = window.siteBuilderCore.pages.length;
            window.siteBuilderCore.showMessage(
                `Welcome to xDREAM Site Builder! ${pageCount} pages loaded.`, 
                'success'
            );
        }
    }

    showError(message) {
        console.error('❌', message);
        
        // Try to show error message if core is available
        if (window.siteBuilderCore) {
            window.siteBuilderCore.showMessage(message, 'error');
        } else {
            // Fallback error display
            alert(`Site Builder Error: ${message}`);
        }
    }

    // Public API methods
    exportSite() {
        if (window.siteBuilderCore) {
            return window.siteBuilderCore.exportSite();
        }
        return false;
    }

    importSite(file) {
        if (window.siteBuilderCore) {
            return window.siteBuilderCore.importSite(file);
        }
        return false;
    }

    generateSite() {
        if (this.siteGenerator) {
            return this.siteGenerator.generateFullSite();
        }
        return false;
    }

    getVersion() {
        return this.version;
    }

    getStatus() {
        return {
            initialized: this.initialized,
            version: this.version,
            pageCount: window.siteBuilderCore ? window.siteBuilderCore.pages.length : 0,
            components: {
                core: !!window.siteBuilderCore,
                ui: !!window.siteBuilderUI,
                templates: !!window.siteBuilderTemplates,
                generator: !!this.siteGenerator
            }
        };
    }
}

// Site Generator - Generates the actual website files
class SiteGenerator {
    constructor() {
        this.outputPath = '../generated/';
        console.log('🏗️ Site Generator ready');
    }

    generateFullSite() {
        try {
            const pages = window.siteBuilderCore.getAllPages();
            const siteData = this.prepareSiteData(pages);
            
            // Generate main index.html
            const indexHTML = this.generateIndexHTML(siteData);
            
            // Generate navigation data
            const navData = this.generateNavigationData(pages);
            
            // Generate pages data for JavaScript
            const pagesJS = this.generatePagesJS(pages);

            console.log('🏗️ Site generation completed');
            
            return {
                indexHTML,
                navData,
                pagesJS,
                pages: pages.length,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Site generation failed:', error);
            return false;
        }
    }

    prepareSiteData(pages) {
        return {
            title: 'xDREAM Gaming',
            description: 'Enter the Gaming Multiverse',
            pages: pages.map(page => ({
                id: page.id,
                title: page.title,
                subtitle: page.subtitle,
                icon: page.icon,
                accentColor: page.accentColor,
                order: page.order
            })),
            totalDimensions: pages.length,
            generated: new Date().toISOString()
        };
    }

    generateIndexHTML(siteData) {
        const pages = window.siteBuilderCore.getAllPages();
        
        // Generate navigation orbs
        const navOrbs = pages.map((page, index) => 
            `<div class="nav-orb${index === 0 ? ' active' : ''}" data-dimension="${index}" title="${page.title}" role="button" tabindex="0" aria-label="Navigate to ${page.title}"${index === 0 ? ' aria-current="true"' : ''}>${page.icon}</div>`
        ).join('\n        ');

        // Generate dimension indicators
        const indicators = pages.map((page, index) => 
            `<div class="indicator-dot${index === 0 ? ' active' : ''}" data-dimension="${index}" role="tab" tabindex="0" aria-selected="${index === 0 ? 'true' : 'false'}" aria-label="${page.title} dimension"></div>`
        ).join('\n        ');

        // Generate page sections
        const sections = pages.map((page, index) => {
            const content = this.generatePageContent(page);
            const className = index === 0 ? 'dimension home-dimension' : `dimension game-dimension ${page.id}-dimension`;
            
            return `
        <!-- ${page.title} Dimension -->
        <section class="${className}" role="tabpanel" aria-label="${page.title}">
            <div class="dimension-content">
                ${content}
            </div>
        </section>`;
        }).join('\n');

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>${siteData.title} - Enter the Future</title>

    <!-- SECURITY FIX: Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://discord.xdreamserver.com https://store.xdreamserver.com; font-src 'self'; manifest-src 'self';">

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="${siteData.title}">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="${siteData.title}">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#00ffff">
    <meta name="msapplication-TileColor" content="#00ffff">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjMDAwIi8+Cjx0ZXh0IHg9IjE2IiB5PSIyMCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzAwZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+WEQ8L3RleHQ+Cjwvc3ZnPgo=">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://discord.xdreamserver.com">
    <link rel="preconnect" href="https://store.xdreamserver.com">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/dimensions.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen" role="status" aria-live="polite">
        <div class="loading-spinner"></div>
        <div class="loading-text">INITIALIZING QUANTUM MATRIX...</div>
    </div>

    <!-- Matrix Background -->
    <canvas class="matrix-bg" id="matrix" aria-hidden="true"></canvas>

    <!-- Navigation Portal -->
    <nav class="nav-portal" role="navigation" aria-label="Game dimensions navigation">
        ${navOrbs}
    </nav>

    <!-- Dimension Indicator -->
    <div class="dimension-indicator" role="tablist" aria-label="Dimension indicators">
        ${indicators}
    </div>

    <!-- Main Universe Container -->
    <main class="universe" id="universe" role="main">
${sections}
    </main>

    <!-- Site Builder Access -->
    <div class="site-builder-access" style="position: fixed; bottom: 20px; right: 20px; z-index: 1000;">
        <button class="quantum-button" onclick="window.location.href='site-builder/index.html'" title="Open Site Builder">
            🛠️ EDIT SITE
        </button>
    </div>

    <!-- JavaScript Files -->
    <script src="scripts/utils.js"></script>
    <script src="scripts/pwa.js"></script>
    <script src="scripts/navigation.js"></script>
    <script src="scripts/effects.js"></script>
    <script src="scripts/main.js"></script>
    
    <!-- Generated site data -->
    <script>
        window.xDreamSiteData = ${JSON.stringify(siteData, null, 8)};
    </script>
</body>
</html>`;
    }

    generatePageContent(page) {
        if (!page.content || page.content.length === 0) {
            return `
                <div class="dimension-header">
                    <h2 class="dimension-title">${page.title}</h2>
                    <div class="dimension-subtitle">${page.subtitle || ''}</div>
                </div>
            `;
        }

        return page.content.map(block => {
            switch (block.type) {
                case 'text':
                case 'custom':
                    return block.content || '';
                
                case 'server-node':
                    return `
                <div class="server-matrix">
                    <div class="server-node ${page.id}-node">
                        <div class="server-name">${block.serverName || 'Server Name'}</div>
                        <button class="server-address" onclick="copyToClipboard('${block.serverAddress || ''}')" aria-label="Copy server address ${block.serverAddress || ''}">
                            ${block.serverAddress || 'server.address.com:port'}
                        </button>
                        <div class="server-info">${block.serverInfo || 'Server Info'}</div>
                    </div>
                </div>`;
                
                case 'button':
                    return `
                <div class="button-group">
                    <button class="${block.style || 'quantum-button'}" onclick="window.open('${block.url || '#'}', '_blank')" aria-label="${block.text || 'Button'}">
                        ${block.text || 'Button'}
                    </button>
                </div>`;
                
                default:
                    return '';
            }
        }).join('\n                ');
    }

    generateNavigationData(pages) {
        return {
            dimensions: pages.map((page, index) => ({
                id: page.id,
                index: index,
                title: page.title,
                icon: page.icon,
                accentColor: page.accentColor
            })),
            totalDimensions: pages.length
        };
    }

    generatePagesJS(pages) {
        return `// Generated pages data
window.xDreamPages = ${JSON.stringify(pages, null, 4)};

// Update navigation system
if (window.xDreamState) {
    window.xDreamState.totalDimensions = ${pages.length};
}`;
    }

    downloadGeneratedSite(siteData) {
        // Create a zip file with all generated content
        const files = {
            'index.html': siteData.indexHTML,
            'data/navigation.json': JSON.stringify(siteData.navData, null, 2),
            'scripts/generated-pages.js': siteData.pagesJS
        };

        // For now, just download the main HTML file
        const blob = new Blob([siteData.indexHTML], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'index.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('📥 Generated site downloaded');
        return true;
    }
}

// Initialize Site Builder
window.siteBuilder = new SiteBuilder();

// Export for global access
window.SiteBuilder = SiteBuilder;
window.SiteGenerator = SiteGenerator;