/* styles/main.css - Core styles with critical layout fixes */

/* CSS Variables with fallbacks - Updated for Orange/Red Theme */
:root {
    --primary-glow: #ff6600;
    --secondary-glow: #ff3300;
    --tertiary-glow: #cc4400;
    --bg-dark: #0a0a0a;
    --bg-darker: #000000;
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);

    /* Game-specific accent colors - Updated for Orange/Red Theme */
    --palworld-accent: #ff6600;
    --sevendays-accent: #ff3300;
    --zomboid-accent: #cc4400;
    --discord-accent: #ff6600;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    
    /* Typography */
    --font-primary: 'Courier New', monospace;
    --font-size-xs: 0.875rem;
    --font-size-sm: 1rem;
    --font-size-md: 1.2rem;
    --font-size-lg: 1.5rem;
    --font-size-xl: 2rem;
    --font-size-2xl: 3rem;
    --font-size-3xl: 4rem;
    --font-size-4xl: 6rem;
    
    /* Transitions */
    --transition-fast: 0.3s ease;
    --transition-medium: 0.5s ease;
    --transition-slow: 1s ease;
}

/* CRITICAL FIX: CSS Variable Fallbacks for older browsers - Updated for Orange/Red Theme */
@supports not (color: var(--primary-glow)) {
    * {
        --primary-glow: #ff6600;
        --secondary-glow: #ff3300;
        --bg-dark: #0a0a0a;
        --bg-darker: #000000;
        --text-primary: #ffffff;
    }
}

/* Reset and Base Styles with better normalization */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    /* CRITICAL FIX: Prevent horizontal overflow */
    overflow-x: hidden;
    /* PERFORMANCE FIX: Better font rendering */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    /* CRITICAL FIX: Ensure full height is available */
    height: 100%;
}

body {
    font-family: var(--font-primary, 'Courier New', monospace);
    background: var(--bg-darker, #050505);
    color: var(--text-primary, #ffffff);
    
    /* CRITICAL FIX: Better viewport handling - use relative positioning for desktop too */
    height: 100vh;
    height: 100dvh;
    width: 100vw;
    position: relative; /* Changed from fixed to relative */
    
    /* CRITICAL FIX: Allow proper scrolling behavior */
    overflow: hidden;
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
    
    /* COMPATIBILITY FIX: Touch action for better mobile support */
    touch-action: pan-x;
    
    /* ACCESSIBILITY FIX: Only hide cursor on capable devices */
    cursor: default;
    
    /* CRITICAL FIX: Ensure content starts at top */
    margin: 0;
    padding: 0;
}

/* COMPATIBILITY FIX: Hide cursor only on hover-capable devices */
@media (hover: hover) and (pointer: fine) {
    body {
        cursor: none;
    }
}

/* Typography Base with better fallbacks */
h1, h2, h3, h4, h5, h6 {
    font-weight: bold;
    line-height: 1.2;
    /* PERFORMANCE FIX: Better text rendering */
    text-rendering: optimizeLegibility;
}

h1 { 
    font-size: var(--font-size-4xl, 6rem); 
    font-size: clamp(2rem, 8vw, 6rem); /* Responsive sizing */
}
h2 { 
    font-size: var(--font-size-3xl, 4rem);
    font-size: clamp(1.5rem, 6vw, 4rem);
}
h3 { 
    font-size: var(--font-size-2xl, 3rem);
    font-size: clamp(1.25rem, 4vw, 3rem);
}
h4 { 
    font-size: var(--font-size-xl, 2rem);
    font-size: clamp(1.125rem, 3vw, 2rem);
}

p {
    line-height: 1.6;
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    /* PERFORMANCE FIX: Better text wrapping */
    word-wrap: break-word;
    hyphens: auto;
}

/* Links with better accessibility - Updated for Orange/Red Theme */
a {
    color: var(--primary-glow, #ff6600);
    text-decoration: none;
    transition: color var(--transition-fast, 0.3s ease);
}

a:hover,
a:focus {
    color: var(--secondary-glow, #ff3300);
    outline: 2px solid transparent;
}

/* ACCESSIBILITY FIX: Focus styles for keyboard navigation - Updated for Orange/Red Theme */
a:focus,
button:focus,
[tabindex]:focus {
    outline: 2px solid var(--primary-glow, #ff6600);
    outline-offset: 2px;
}

/* Custom Cursor with performance optimizations - Updated for Orange/Red Theme */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: radial-gradient(circle, var(--primary-glow, #ff6600), transparent);
    border-radius: 50%;
    pointer-events: none;
    z-index: 10000;
    /* PERFORMANCE FIX: Better compositing */
    will-change: transform;
    backface-visibility: hidden;
    /* COMPATIBILITY FIX: Better blend mode fallback */
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

.cursor.active {
    transform: scale(2);
    background: radial-gradient(circle, var(--secondary-glow, #ff3300), transparent);
}

/* CRITICAL FIX: Hide cursor on touch devices and unsupported browsers */
@media (hover: none), (pointer: coarse) {
    .cursor {
        display: none !important;
    }
}

/* Main Container with critical layout fixes */
.universe {
    display: flex;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height */
    width: 600vw; /* 6 dimensions × 100vw */
    
    /* PERFORMANCE FIX: Better transform handling */
    transition: transform 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: transform;
    backface-visibility: hidden;
    transform-style: preserve-3d;
    
    /* CRITICAL FIX: Proper positioning - ensure it starts at top */
    position: absolute;
    top: 0;
    left: 0;
    
    /* COMPATIBILITY FIX: Prevent layout shifts */
    contain: layout style paint;
}

/* CRITICAL FIX: Enable scrolling for dimensions but ensure proper height */
.dimension {
    width: 100vw;
    height: 100vh; /* CRITICAL FIX: Use fixed height for proper display */
    height: 100dvh;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    position: relative;
    
    /* CRITICAL FIX: Allow vertical scrolling, prevent horizontal overflow */
    overflow-x: hidden;
    overflow-y: auto;
    
    /* PERFORMANCE FIX: Better compositing */
    will-change: transform;
    backface-visibility: hidden;
    flex-shrink: 0;
    
    /* COMPATIBILITY FIX: Better containment */
    contain: layout style;
}

/* CRITICAL FIX: Home dimension should be properly centered and visible */
.home-dimension {
    overflow: hidden;
    height: 100vh;
    height: 100dvh;
    align-items: center;
    justify-content: center;
    
    /* CRITICAL FIX: Ensure content is properly positioned */
    padding: var(--spacing-lg, 2rem);
    
    /* CRITICAL FIX: Add flex properties to center content properly */
    display: flex;
    flex-direction: column;
}

/* Matrix Background with performance fixes */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
    
    /* PERFORMANCE FIX: Better GPU acceleration */
    will-change: auto;
    transform: translateZ(0);
}

/* Utility Classes with better fallbacks */
.button-group {
    display: flex;
    gap: var(--spacing-sm, 1rem);
    margin-top: var(--spacing-xl, 3rem);
    justify-content: center;
    flex-wrap: wrap;
    
    /* COMPATIBILITY FIX: Flexbox fallback */
    align-items: center;
}

/* COMPATIBILITY FIX: Flexbox fallback for older browsers */
@supports not (display: flex) {
    .button-group {
        text-align: center;
    }
    
    .button-group > * {
        display: inline-block;
        margin: 0.5rem;
        vertical-align: middle;
    }
}

/* CRITICAL FIX: Better PWA viewport handling */
@supports (height: 100dvh) {
    body,
    .universe {
        height: 100dvh;
    }
    
    .dimension {
        height: 100dvh;
    }
    
    .home-dimension {
        height: 100dvh;
    }
}

/* PWA Support with better safe area handling */
@supports (display-mode: standalone) {
    body {
        padding-top: env(safe-area-inset-top, 0);
        padding-bottom: env(safe-area-inset-bottom, 0);
        padding-left: env(safe-area-inset-left, 0);
        padding-right: env(safe-area-inset-right, 0);
    }
}

/* CRITICAL FIX: Desktop-specific adjustments */
@media (min-width: 769px) {
    .home-dimension {
        /* CRITICAL FIX: Ensure home content is properly centered on desktop */
        min-height: 100vh;
        min-height: 100dvh;
        padding: var(--spacing-xl, 3rem);
    }
    
    /* CRITICAL FIX: Ensure logo and content are visible */
    .logo-matrix {
        margin-top: 0;
        margin-bottom: var(--spacing-lg, 2rem);
    }
    
    .tagline {
        margin-bottom: var(--spacing-xl, 3rem);
    }
}

/* CRITICAL FIX: Landscape orientation handling */
@media (orientation: landscape) and (max-height: 500px) {
    :root {
        --font-size-4xl: 3rem;
        --font-size-3xl: 2rem;
        --font-size-2xl: 1.5rem;
    }
    
    .home-dimension {
        padding: var(--spacing-md, 1.5rem);
    }
}

/* PERFORMANCE FIX: Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .cursor {
        transition: none;
    }
    
    .universe {
        transition: none;
    }
}

/* COMPATIBILITY FIX: High contrast mode support - Updated for Orange/Red Theme */
@media (prefers-contrast: high) {
    :root {
        --primary-glow: #ff6600;
        --secondary-glow: #ff3300;
        --text-secondary: #ffffff;
    }

    body {
        background: #000000;
        color: #ffffff;
    }
}

/* PERFORMANCE FIX: Print styles to prevent printing issues */
@media print {
    .cursor,
    .matrix-bg,
    .loading-screen,
    .nav-portal,
    .dimension-indicator {
        display: none !important;
    }
    
    body {
        position: static;
        overflow: visible;
        background: white;
        color: black;
    }
    
    .universe {
        position: static;
        width: auto;
        height: auto;
        display: block;
    }
    
    .dimension {
        width: auto;
        height: auto;
        page-break-after: always;
    }
}

/* CRITICAL FIX: Browser-specific fixes */
/* Firefox */
@-moz-document url-prefix() {
    body {
        scrollbar-width: none;
    }
}

/* Safari */
@supports (-webkit-appearance: none) {
    body {
        -webkit-overflow-scrolling: touch;
    }
}

/* Edge/IE fallbacks */
@supports (-ms-ime-align: auto) {
    .universe {
        -ms-overflow-style: none;
    }
}

/* Discord logo styling */
.discord-logo-container {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.discord-logo {
    width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8rem;
    background: linear-gradient(135deg, #5865f2, #7289da);
    border-radius: 50%;
    box-shadow: 0 0 30px rgba(88, 101, 242, 0.5);
    transition: all 0.3s ease;
    position: relative;
    z-index: 5;
}

.discord-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 0 50px rgba(88, 101, 242, 0.7);
}

/* MOBILE RESPONSIVE STYLES - Clean and maintainable */
@media (max-width: 768px) {
    /* Mobile body and html setup */
    html, body {
        width: 100vw;
        height: 100vh;
        height: 100dvh;
        overflow-x: hidden;
        overflow-y: hidden;
        position: relative;
        margin: 0;
        padding: 0;
    }

    /* Universe container for mobile */
    .universe {
        position: relative;
        display: flex;
        width: 600vw;
        height: 100vh;
        height: 100dvh;
        top: 0;
        left: 0;
        overflow: visible;
        flex-direction: row;
    }

    /* Dimension containers for mobile */
    .dimension {
        width: 100vw;
        height: 100vh;
        height: 100dvh;
        min-height: 100vh;
        min-height: 100dvh;
        position: relative;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        overflow-x: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        background: rgba(5, 5, 5, 0.98);
    }

    /* Home dimension mobile layout */
    .home-dimension {
        justify-content: flex-start;
        align-items: center;
        text-align: center;
        padding: 20px 1rem;
        padding-top: 60px; /* Space for navigation */
        overflow-y: auto;
        background: rgba(5, 5, 5, 0.98);
    }

    /* Mobile logo styling */
    .logo-matrix {
        font-size: 2.5rem;
        margin: 1rem 0;
        color: var(--primary-glow, #00ffff);
        text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
    }

    /* Mobile tagline */
    .tagline {
        font-size: 1.2rem;
        margin: 1rem 0;
        color: var(--text-primary, #ffffff);
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }

    /* Game dimensions mobile layout */
    .game-dimension {
        justify-content: flex-start;
        align-items: stretch;
        padding: 80px 1rem 20px 1rem; /* Top padding for navigation */
        overflow-y: auto;
        overflow-x: hidden;
        background: rgba(10, 10, 10, 0.98);
    }

    /* Mobile dimension content */
    .dimension-content {
        width: 100%;
        max-width: 100%;
        padding: 0 1rem;
        margin: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Mobile dimension headers */
    .dimension-header {
        width: 100%;
        text-align: center;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 10px;
    }

    /* Mobile game logos */
    .game-logo {
        width: 120px;
        height: 120px;
        display: block;
        margin: 0 auto 1rem auto;
    }

    /* Mobile Discord logo */
    .discord-logo {
        width: 120px;
        height: 120px;
        font-size: 4rem;
    }

    /* Mobile dimension titles */
    .dimension-title {
        font-size: 1.8rem;
        color: var(--primary-glow, #00ffff);
        margin: 1rem 0;
        text-align: center;
        background: rgba(0, 0, 0, 0.9);
        padding: 1rem;
        border-radius: 8px;
    }

    /* Mobile subtitles */
    .dimension-subtitle {
        font-size: 1rem;
        color: var(--text-primary, #ffffff);
        margin: 0.5rem 0;
        text-align: center;
    }

    /* Mobile content sections */
    .server-matrix,
    .server-settings-container,
    .custom-passives-section,
    .quick-links-section,
    .zomboid-setup-section,
    .economy-section,
    .community-section,
    .dune-overview-section,
    .connection-section,
    .server-settings-section,
    .world-structure-section,
    .community-rules-section,
    .join-community-section,
    .discord-overview-section,
    .discord-features-section,
    .discord-commands-section,
    .discord-stats-section,
    .join-discord-section,
    .compact-instructions {
        width: 100%;
        max-width: 95%;
        margin: 1rem auto;
        padding: 1rem;
        background: rgba(0, 0, 0, 0.7);
        border: 1px solid var(--primary-glow, #00ffff);
        border-radius: 10px;
    }

    /* Mobile server nodes - handled by components.css responsive rules */

    /* Mobile server names */
    .server-name {
        font-size: 1.2rem;
        color: var(--primary-glow, #00ffff);
        text-align: center;
        margin: 1rem 0;
        font-weight: bold;
    }

    /* Mobile server addresses */
    .server-address,
    .connect-address {
        width: 100%;
        max-width: 100%;
        background: rgba(0, 0, 0, 0.9);
        color: var(--text-primary, #ffffff);
        border: 2px solid var(--primary-glow, #00ffff);
        padding: 1rem;
        margin: 0.5rem 0;
        font-family: monospace;
        font-size: 0.9rem;
        text-align: center;
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 44px;
        word-break: break-all;
    }

    /* Mobile text content */
    .server-info,
    .section-title,
    .settings-title,
    .compact-title,
    .card-content,
    .setup-step,
    .economy-content,
    .feature-details p,
    .command-desc,
    .stat-label,
    .structure-details p,
    .rule-item,
    .step-content,
    .overview-card p,
    .community-content,
    .discord-content p,
    .passive-desc,
    .setting-item,
    .eac-warning,
    .setup-note {
        color: var(--text-primary, #ffffff);
        font-size: 0.9rem;
        line-height: 1.4;
        margin: 0.5rem 0;
    }

    /* Mobile section titles */
    .section-title,
    .settings-title,
    .compact-title {
        font-size: 1.5rem;
        color: var(--primary-glow, #00ffff);
        text-align: center;
        margin: 1rem 0;
        font-weight: bold;
    }

    /* Mobile buttons and interactive elements */
    .quantum-button,
    .link-btn,
    .community-btn,
    .discord-btn,
    .game-portal {
        width: 100%;
        max-width: 300px;
        margin: 0.5rem auto;
        padding: 1rem;
        background: rgba(0, 255, 255, 0.1);
        border: 2px solid var(--primary-glow, #00ffff);
        color: var(--text-primary, #ffffff);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-height: 44px;
        font-size: 1rem;
        cursor: pointer;
    }

    /* Mobile game portals */
    .game-portal {
        height: 100px;
        flex-direction: row;
        padding: 1rem;
        margin: 1rem auto;
    }

    /* Mobile game icons in portals */
    .game-icon-img {
        width: 50px;
        height: 50px;
        margin-right: 1rem;
        margin-bottom: 0;
    }

    /* Mobile game titles */
    .game-title {
        font-size: 1rem;
        color: var(--text-primary, #ffffff);
    }

    /* Mobile player counts */
    .player-count {
        background: rgba(0, 255, 0, 0.2);
        border: 1px solid #00ff00;
        color: #00ff00;
        padding: 0.5rem;
        margin: 0.5rem 0;
        border-radius: 15px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Mobile grids */
    .settings-grid,
    .economy-grid,
    .features-grid,
    .commands-grid,
    .stats-grid,
    .structure-grid,
    .connection-methods,
    .quick-link-grid,
    .instruction-grid,
    .passives-grid,
    .server-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
        width: 100%;
        margin: 1rem auto;
    }

    /* Mobile cards and panels */
    .settings-panel,
    .economy-card,
    .feature-card,
    .command-card,
    .stat-card,
    .structure-card,
    .rules-panel,
    .connection-method,
    .instruction-card,
    .setup-card,
    .community-card,
    .overview-card,
    .discord-join-card,
    .passive-item {
        background: rgba(0, 0, 0, 0.8);
        border: 1px solid var(--primary-glow, #00ffff);
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem auto;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    /* Mobile navigation */
    .nav-portal {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: rgba(0, 0, 0, 0.9);
        padding: 5px;
        border-radius: 10px;
        display: flex;
        gap: 5px;
    }

    .nav-orb {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
    }

    /* Mobile scrollbar styling */
    .dimension::-webkit-scrollbar {
        width: 8px;
    }

    .dimension::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.3);
    }

    .dimension::-webkit-scrollbar-thumb {
        background: rgba(0, 255, 255, 0.5);
        border-radius: 4px;
    }

    /* Hide performance-heavy elements on mobile */
    .matrix-bg {
        display: none;
    }

    .zombie-particles,
    .sand-particles {
        display: none;
    }
}