<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>xDREAM Site Builder - Advanced Gaming Portal Management</title>
    
    <!-- Security -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;">
    
    <!-- Theme -->
    <meta name="theme-color" content="#00ffff">
    <meta name="description" content="Advanced site builder for xDREAM Gaming portal with real-time editing and preview">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjMyIiBoZWlnaHQ9IjMyIiBmaWxsPSIjMDAwIi8+Cjx0ZXh0IHg9IjE2IiB5PSIyMCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzAwZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+U0I8L3RleHQ+Cjwvc3ZnPgo=">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="../styles/main.css">
    <link rel="stylesheet" href="../styles/components.css">
    <link rel="stylesheet" href="../styles/dimensions.css">
    <link rel="stylesheet" href="../styles/responsive.css">
    <link rel="stylesheet" href="site-builder.css">
    
    <!-- Enhanced Site Builder Styles -->
    <style>
        /* Enhanced Site Builder Specific Styles */
        .quick-actions-bar {
            display: flex;
            gap: var(--spacing-sm, 1rem);
            margin-bottom: var(--spacing-lg, 2rem);
            padding: var(--spacing-sm, 1rem);
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--primary-glow, #00ffff);
            border-radius: 8px;
            flex-wrap: wrap;
        }
        
        .quick-action-btn {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid var(--primary-glow, #00ffff);
            color: var(--text-primary, #ffffff);
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all var(--transition-fast, 0.3s ease);
            font-family: var(--font-primary, 'Courier New', monospace);
            font-size: var(--font-size-sm, 1rem);
        }
        
        .quick-action-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
        }
        
        .quick-action-btn.danger {
            border-color: #ff0000;
            background: rgba(255, 0, 0, 0.1);
        }
        
        .quick-action-btn.danger:hover {
            background: rgba(255, 0, 0, 0.2);
            box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
        }
        
        .effects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-md, 1.5rem);
            margin-top: var(--spacing-sm, 1rem);
        }
        
        .effect-option {
            cursor: pointer;
            display: block;
        }
        
        .effect-option input[type="checkbox"] {
            display: none;
        }
        
        .effect-card {
            background: rgba(0, 0, 0, 0.5);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: var(--spacing-md, 1.5rem);
            text-align: center;
            transition: all var(--transition-fast, 0.3s ease);
        }
        
        .effect-option input[type="checkbox"]:checked + .effect-card {
            border-color: var(--primary-glow, #00ffff);
            background: rgba(0, 255, 255, 0.1);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }
        
        .effect-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.2);
        }
        
        .effect-icon {
            font-size: var(--font-size-2xl, 3rem);
            margin-bottom: var(--spacing-sm, 1rem);
        }
        
        .effect-name {
            font-weight: bold;
            color: var(--text-primary, #ffffff);
            margin-bottom: var(--spacing-xs, 0.5rem);
        }
        
        .effect-desc {
            font-size: var(--font-size-sm, 1rem);
            color: var(--text-secondary, rgba(255, 255, 255, 0.8));
        }
        
        .content-editor-toolbar {
            background: rgba(0, 255, 255, 0.05);
            border: 1px solid var(--primary-glow, #00ffff);
            border-radius: 8px 8px 0 0;
            padding: var(--spacing-sm, 1rem);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-sm, 1rem);
        }
        
        .toolbar-group {
            display: flex;
            gap: var(--spacing-xs, 0.5rem);
            flex-wrap: wrap;
        }
        
        .content-blocks-container {
            border: 1px solid var(--primary-glow, #00ffff);
            border-radius: 0 0 8px 8px;
            min-height: 200px;
        }
        
        .content-blocks {
            padding: var(--spacing-md, 1.5rem);
        }
        
        .content-block {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            margin-bottom: var(--spacing-md, 1.5rem);
            transition: all var(--transition-fast, 0.3s ease);
        }
        
        .content-block:hover {
            border-color: var(--primary-glow, #00ffff);
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
        }
        
        .content-block.collapsed .content-block-body {
            display: none;
        }
        
        .content-block-header {
            background: rgba(0, 255, 255, 0.1);
            padding: var(--spacing-sm, 1rem);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 8px 8px 0 0;
        }
        
        .block-header-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm, 1rem);
        }
        
        .drag-handle {
            cursor: grab;
            color: var(--text-secondary, rgba(255, 255, 255, 0.6));
            font-size: var(--font-size-lg, 1.5rem);
            line-height: 1;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .block-type-icon {
            font-size: var(--font-size-lg, 1.5rem);
        }
        
        .block-type-name {
            font-weight: bold;
            color: var(--primary-glow, #00ffff);
        }
        
        .block-preview {
            color: var(--text-secondary, rgba(255, 255, 255, 0.8));
            font-size: var(--font-size-sm, 1rem);
            font-style: italic;
        }
        
        .block-header-right {
            display: flex;
            gap: var(--spacing-xs, 0.5rem);
        }
        
        .block-action-btn {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-primary, #ffffff);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all var(--transition-fast, 0.3s ease);
            font-size: var(--font-size-sm, 1rem);
        }
        
        .block-action-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: var(--primary-glow, #00ffff);
        }
        
        .block-action-btn.danger {
            border-color: rgba(255, 0, 0, 0.5);
        }
        
        .block-action-btn.danger:hover {
            background: rgba(255, 0, 0, 0.2);
            border-color: #ff0000;
        }
        
        .content-block-body {
            padding: var(--spacing-md, 1.5rem);
        }
        
        .text-editor-container,
        .custom-editor-container {
            border: 1px solid var(--primary-glow, #00ffff);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .text-editor-toolbar,
        .custom-editor-toolbar {
            background: rgba(0, 255, 255, 0.1);
            padding: var(--spacing-xs, 0.5rem);
            border-bottom: 1px solid var(--primary-glow, #00ffff);
            display: flex;
            gap: var(--spacing-xs, 0.5rem);
        }
        
        .text-btn {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-primary, #ffffff);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all var(--transition-fast, 0.3s ease);
            font-size: var(--font-size-xs, 0.875rem);
        }
        
        .text-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: var(--primary-glow, #00ffff);
        }
        
        .icon-input-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm, 1rem);
        }
        
        .icon-preview {
            font-size: var(--font-size-2xl, 3rem);
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid var(--primary-glow, #00ffff);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.5);
        }
        
        .color-input-group {
            display: flex;
            gap: var(--spacing-sm, 1rem);
        }
        
        .color-input-group input[type="color"] {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .field-help {
            font-size: var(--font-size-xs, 0.875rem);
            color: var(--text-secondary, rgba(255, 255, 255, 0.6));
            margin-top: var(--spacing-xs, 0.5rem);
            font-style: italic;
        }
        
        .button-preview {
            margin-top: var(--spacing-sm, 1rem);
            padding: var(--spacing-sm, 1rem);
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
            text-align: center;
        }
        
        .image-preview {
            margin-top: var(--spacing-sm, 1rem);
            text-align: center;
            padding: var(--spacing-sm, 1rem);
            background: rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }
        
        .empty-content {
            text-align: center;
            padding: var(--spacing-xl, 3rem);
            color: var(--text-secondary, rgba(255, 255, 255, 0.6));
        }
        
        .empty-icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-md, 1.5rem);
        }
        
        .drop-zone {
            text-align: center;
            padding: var(--spacing-lg, 2rem);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: var(--text-secondary, rgba(255, 255, 255, 0.6));
            margin-top: var(--spacing-md, 1.5rem);
        }
        
        .page-actions {
            opacity: 0;
            transition: opacity var(--transition-fast, 0.3s ease);
        }
        
        .page-item:hover .page-actions {
            opacity: 1;
        }
        
        .action-btn {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-primary, #ffffff);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all var(--transition-fast, 0.3s ease);
            font-size: var(--font-size-sm, 1rem);
        }
        
        .action-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: var(--primary-glow, #00ffff);
        }
        
        .action-btn.delete-btn:hover {
            background: rgba(255, 0, 0, 0.2);
            border-color: #ff0000;
        }
        
        .page-meta {
            font-size: var(--font-size-xs, 0.875rem);
            color: var(--text-secondary, rgba(255, 255, 255, 0.6));
            margin-top: var(--spacing-xs, 0.5rem);
            display: flex;
            gap: var(--spacing-sm, 1rem);
        }
        
        .default-badge {
            background: rgba(255, 255, 0, 0.2);
            color: #ffff00;
            padding: 0.125rem 0.5rem;
            border-radius: 12px;
            font-size: var(--font-size-xs, 0.875rem);
        }
        
        .unsaved-indicator {
            color: #ff6600;
            animation: pulse 2s infinite;
        }
        
        .page-status {
            font-size: var(--font-size-sm, 1rem);
            color: var(--text-secondary, rgba(255, 255, 255, 0.7));
        }
        
        /* Enhanced message styles */
        .message-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--spacing-sm, 1rem);
        }
        
        .message-close {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            font-size: var(--font-size-lg, 1.5rem);
            padding: 0;
            opacity: 0.7;
            transition: opacity var(--transition-fast, 0.3s ease);
        }
        
        .message-close:hover {
            opacity: 1;
        }
        
        /* Enhanced form styling */
        .form-section {
            border-left: 4px solid var(--primary-glow, #00ffff);
        }
        
        .form-section-title {
            position: relative;
        }
        
        .form-section-title::before {
            content: '';
            position: absolute;
            left: -2rem;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-glow, #00ffff);
            border-radius: 50%;
            box-shadow: 0 0 10px var(--primary-glow, #00ffff);
        }
        
        /* Responsive enhancements */
        @media (max-width: 768px) {
            .effects-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions-bar {
                flex-direction: column;
                align-items: stretch;
            }
            
            .quick-action-btn {
                text-align: center;
            }
            
            .toolbar-group {
                justify-content: center;
            }
            
            .icon-input-group,
            .color-input-group {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body class="site-builder-body">
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-spinner"></div>
        <div class="loading-text">INITIALIZING ADVANCED SITE BUILDER...</div>
    </div>

    <!-- Matrix Background -->
    <canvas class="matrix-bg" id="matrix" aria-hidden="true"></canvas>

    <!-- Site Builder Interface -->
    <div class="site-builder-container" id="siteBuilderContainer">
        <!-- Enhanced Header -->
        <header class="site-builder-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="site-builder-title glitch">xDREAM SITE BUILDER</h1>
                    <p class="site-builder-subtitle">Advanced Gaming Portal Management</p>
                </div>
                <div class="header-actions">
                    <button class="quantum-button secondary" id="backToSiteBtn" onclick="window.location.href='../index.html'">
                        🏠 BACK TO SITE
                    </button>
                    <button class="quantum-button" id="generateSiteBtn">
                        🚀 GENERATE SITE
                    </button>
                    <button class="quantum-button" id="saveAllBtn">
                        💾 SAVE ALL
                    </button>
                    <button class="quantum-button secondary" id="backupBtn">
                        📦 BACKUP
                    </button>
                    <button class="quantum-button secondary" id="importBtn">
                        📥 IMPORT
                    </button>
                    <button class="quantum-button secondary" id="exportBtn">
                        📤 EXPORT
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="site-builder-main">
            <!-- Enhanced Sidebar -->
            <aside class="site-builder-sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">📄 PAGES</h3>
                    <div class="pages-list" id="pagesList">
                        <!-- Pages will be loaded here dynamically -->
                    </div>
                    <button class="quantum-button add-page-btn" id="addPageBtn">
                        ➕ ADD NEW PAGE
                    </button>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">🎨 QUICK TEMPLATES</h3>
                    <div class="templates-list">
                        <button class="template-btn" data-template="game-server">
                            🎮 Game Server
                        </button>
                        <button class="template-btn" data-template="community">
                            💬 Community Hub
                        </button>
                        <button class="template-btn" data-template="information">
                            📋 Information
                        </button>
                        <button class="template-btn" data-template="event">
                            🎉 Event Page
                        </button>
                        <button class="template-btn" data-template="store">
                            🛒 Store Page
                        </button>
                        <button class="template-btn" data-template="custom">
                            🛠️ Custom Page
                        </button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">📊 SITE STATS</h3>
                    <div class="site-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Pages:</span>
                            <span class="stat-value" id="totalPages">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Last Saved:</span>
                            <span class="stat-value" id="lastSaved">Never</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Site Version:</span>
                            <span class="stat-value">2.0.0</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Enhanced Editor Area -->
            <section class="site-builder-editor">
                <div class="editor-header">
                    <h2 class="editor-title" id="editorTitle">Select a page to edit</h2>
                    <div class="editor-actions">
                        <button class="quantum-button secondary" id="previewBtn" disabled>
                            👁️ PREVIEW
                        </button>
                        <button class="quantum-button" id="savePageBtn" disabled>
                            💾 SAVE PAGE
                        </button>
                        <button class="quantum-button danger" id="deletePageBtn" disabled>
                            🗑️ DELETE
                        </button>
                    </div>
                </div>

                <div class="editor-content" id="editorContent">
                    <div class="editor-placeholder">
                        <div class="placeholder-icon">🚀</div>
                        <h3>Welcome to xDREAM Site Builder v2.0</h3>
                        <p>Select a page from the sidebar to start editing, or create a new page using the templates.</p>
                        <div class="placeholder-actions">
                            <button class="quantum-button" onclick="siteBuilderUI.showAddPageModal()">
                                ➕ Create New Page
                            </button>
                            <button class="quantum-button secondary" onclick="window.open('../index.html', '_blank')">
                                👁️ View Current Site
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Enhanced Preview Panel -->
            <section class="site-builder-preview" id="previewPanel">
                <div class="preview-header">
                    <h3 class="preview-title">🔍 LIVE PREVIEW</h3>
                    <div class="preview-controls">
                        <button class="preview-control-btn" id="refreshPreviewBtn" title="Refresh Preview">
                            🔄
                        </button>
                        <button class="preview-control-btn" id="fullscreenPreviewBtn" title="Fullscreen Preview">
                            ⛶
                        </button>
                        <button class="close-preview-btn" id="closePreviewBtn">✕</button>
                    </div>
                </div>
                <div class="preview-content" id="previewContent">
                    <div class="preview-placeholder">
                        <div class="placeholder-icon">👁️</div>
                        <p>Live preview will appear here when you edit a page</p>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Enhanced Modals -->
    <!-- Add Page Modal -->
    <div class="modal-overlay" id="addPageModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">➕ Create New Page</h3>
                <button class="modal-close" onclick="closeModal('addPageModal')">✕</button>
            </div>
            <div class="modal-body">
                <form id="addPageForm">
                    <div class="form-section">
                        <h4 class="form-section-title">📝 Basic Information</h4>
                        <div class="form-group">
                            <label for="pageTitle">Page Title *</label>
                            <input type="text" id="pageTitle" name="pageTitle" required placeholder="Enter page title" maxlength="50">
                            <div class="field-help">This will appear in navigation and page headers</div>
                        </div>
                        <div class="form-group">
                            <label for="pageSubtitle">Page Subtitle</label>
                            <input type="text" id="pageSubtitle" name="pageSubtitle" placeholder="Enter page subtitle" maxlength="100">
                            <div class="field-help">Optional subtitle shown below the main title</div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="pageIcon">Navigation Icon *</label>
                                <input type="text" id="pageIcon" name="pageIcon" placeholder="🎮" maxlength="2" required>
                                <div class="field-help">Emoji for navigation (max 2 characters)</div>
                            </div>
                            <div class="form-group">
                                <label for="accentColor">Accent Color</label>
                                <input type="color" id="accentColor" name="accentColor" value="#00ffff">
                                <div class="field-help">Primary color for this page</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h4 class="form-section-title">🎨 Template</h4>
                        <div class="form-group">
                            <label for="pageTemplate">Choose Template *</label>
                            <select id="pageTemplate" name="pageTemplate" required>
                                <option value="">Select a template</option>
                                <option value="game-server">🎮 Game Server - Perfect for server information</option>
                                <option value="community">💬 Community Hub - Discord and social pages</option>
                                <option value="information">📋 Information - Guides and documentation</option>
                                <option value="event">🎉 Event Page - Tournaments and special events</option>
                                <option value="store">🛒 Store Page - Products and donations</option>
                                <option value="custom">🛠️ Custom - Blank template for full customization</option>
                            </select>
                            <div class="field-help">Templates provide pre-built content structures</div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="quantum-button secondary" onclick="closeModal('addPageModal')">
                    CANCEL
                </button>
                <button type="submit" form="addPageForm" class="quantum-button">
                    CREATE PAGE
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal-overlay" id="deleteConfirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">⚠️ Confirm Deletion</h3>
                <button class="modal-close" onclick="closeModal('deleteConfirmModal')">✕</button>
            </div>
            <div class="modal-body">
                <div class="warning-content">
                    <div class="warning-icon">🗑️</div>
                    <p>Are you sure you want to delete this page?</p>
                    <p><strong>This action cannot be undone.</strong></p>
                    <div class="delete-page-info">
                        <span class="delete-page-name" id="deletePageName"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="quantum-button secondary" onclick="closeModal('deleteConfirmModal')">
                    CANCEL
                </button>
                <button type="button" class="quantum-button danger" id="confirmDeleteBtn">
                    DELETE PAGE
                </button>
            </div>
        </div>
    </div>

    <!-- Import/Export Modal -->
    <div class="modal-overlay" id="importExportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">📁 Import/Export Site</h3>
                <button class="modal-close" onclick="closeModal('importExportModal')">✕</button>
            </div>
            <div class="modal-body">
                <div class="import-export-tabs">
                    <button class="tab-btn active" onclick="switchTab('import')">📥 Import Site</button>
                    <button class="tab-btn" onclick="switchTab('export')">📤 Export Site</button>
                </div>
                
                <div class="tab-content" id="importTab">
                    <h4>Import Site Configuration</h4>
                    <p>Upload a previously exported site configuration file to restore your pages.</p>
                    <div class="file-input-group">
                        <input type="file" id="importFile" accept=".json" style="display: none;">
                        <button class="quantum-button" onclick="document.getElementById('importFile').click()">
                            📁 Choose File
                        </button>
                        <span id="importFileName">No file selected</span>
                    </div>
                    <div class="import-warning">
                        <strong>⚠️ Warning:</strong> Importing will replace all current pages!
                    </div>
                </div>
                
                <div class="tab-content" id="exportTab" style="display: none;">
                    <h4>Export Site Configuration</h4>
                    <p>Download a backup of your current site configuration including all pages and settings.</p>
                    <button class="quantum-button" id="exportSiteBtn">
                        📤 Export Site
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="quantum-button secondary" onclick="closeModal('importExportModal')">
                    CLOSE
                </button>
                <button type="button" class="quantum-button" id="importSiteBtn" style="display: none;">
                    📥 IMPORT SITE
                </button>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div class="message-container" id="messageContainer"></div>

    <!-- File input for import -->
    <input type="file" id="hiddenFileInput" accept=".json" style="display: none;">

    <!-- Scripts -->
    <script src="../scripts/utils.js"></script>
    <script src="../scripts/effects.js"></script>
    <script src="site-builder-core.js"></script>
    <script src="site-builder-ui.js"></script>
    <script src="site-builder-templates.js"></script>
    <script src="site-builder.js"></script>
    
    <!-- Enhanced functionality scripts -->
    <script>
        // Modal utility functions
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('active');
            }
        }
        
        // Tab switching for import/export modal
        function switchTab(tabName) {
            const tabs = document.querySelectorAll('.tab-btn');
            const contents = document.querySelectorAll('.tab-content');
            const importBtn = document.getElementById('importSiteBtn');
            
            tabs.forEach(tab => tab.classList.remove('active'));
            contents.forEach(content => content.style.display = 'none');
            
            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).style.display = 'block';
            
            if (tabName === 'import') {
                importBtn.style.display = 'inline-block';
            } else {
                importBtn.style.display = 'none';
            }
        }
        
        // Enhanced initialization
        document.addEventListener('DOMContentLoaded', () => {
            // Setup import/export functionality
            setupImportExport();
            
            // Setup additional event listeners
            setupAdditionalEventListeners();
            
            // Update site stats periodically
            updateSiteStats();
            setInterval(updateSiteStats, 30000); // Update every 30 seconds
        });
        
        function setupImportExport() {
            const importBtn = document.getElementById('importBtn');
            const exportBtn = document.getElementById('exportBtn');
            const importFileInput = document.getElementById('importFile');
            const importSiteBtn = document.getElementById('importSiteBtn');
            const exportSiteBtn = document.getElementById('exportSiteBtn');
            
            if (importBtn) {
                importBtn.addEventListener('click', () => {
                    document.getElementById('importExportModal').classList.add('active');
                    switchTab('import');
                });
            }
            
            if (exportBtn) {
                exportBtn.addEventListener('click', () => {
                    document.getElementById('importExportModal').classList.add('active');
                    switchTab('export');
                });
            }
            
            if (importFileInput) {
                importFileInput.addEventListener('change', (e) => {
                    const fileName = e.target.files[0]?.name || 'No file selected';
                    document.getElementById('importFileName').textContent = fileName;
                });
            }
            
            if (importSiteBtn) {
                importSiteBtn.addEventListener('click', async () => {
                    const fileInput = document.getElementById('importFile');
                    const file = fileInput.files[0];
                    
                    if (!file) {
                        alert('Please select a file to import.');
                        return;
                    }
                    
                    if (confirm('This will replace all current pages. Are you sure?')) {
                        const success = await window.siteBuilderCore.importSite(file);
                        if (success && window.siteBuilderUI) {
                            window.siteBuilderUI.renderPagesList();
                            window.siteBuilderUI.clearEditor();
                            closeModal('importExportModal');
                        }
                    }
                });
            }
            
            if (exportSiteBtn) {
                exportSiteBtn.addEventListener('click', () => {
                    window.siteBuilderCore.exportSite();
                });
            }
        }
        
        function setupAdditionalEventListeners() {
            // Generate Site button
            const generateSiteBtn = document.getElementById('generateSiteBtn');
            if (generateSiteBtn) {
                generateSiteBtn.addEventListener('click', () => {
                    if (window.siteBuilderCore) {
                        window.siteBuilderCore.downloadGeneratedSite();
                    }
                });
            }
            
            // Preview controls
            const refreshPreviewBtn = document.getElementById('refreshPreviewBtn');
            const fullscreenPreviewBtn = document.getElementById('fullscreenPreviewBtn');
            
            if (refreshPreviewBtn && window.siteBuilderUI) {
                refreshPreviewBtn.addEventListener('click', () => {
                    window.siteBuilderUI.updatePreview();
                });
            }
            
            if (fullscreenPreviewBtn && window.siteBuilderUI) {
                fullscreenPreviewBtn.addEventListener('click', () => {
                    // Toggle fullscreen preview
                    const previewPanel = document.getElementById('previewPanel');
                    if (previewPanel) {
                        previewPanel.classList.toggle('fullscreen');
                    }
                });
            }
        }
        
        function updateSiteStats() {
            if (window.siteBuilderCore) {
                const totalPagesEl = document.getElementById('totalPages');
                const lastSavedEl = document.getElementById('lastSaved');
                
                if (totalPagesEl) {
                    totalPagesEl.textContent = window.siteBuilderCore.pages.length;
                }
                
                if (lastSavedEl) {
                    const lastModified = localStorage.getItem('xdream-site-builder-last-save');
                    if (lastModified) {
                        const date = new Date(lastModified);
                        lastSavedEl.textContent = date.toLocaleTimeString();
                    }
                }
            }
        }
        
        // Auto-save indication
        window.addEventListener('beforeunload', (e) => {
            localStorage.setItem('xdream-site-builder-last-save', new Date().toISOString());
        });
    </script>
</body>
</html>