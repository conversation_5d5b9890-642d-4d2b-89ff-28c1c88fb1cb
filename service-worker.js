// service-worker.js - Service Worker with cache busting and update mechanism

// CRITICAL FIX: Update version when deploying changes
const CACHE_VERSION = 'xdream-v1.1.0'; // Increment this version number when updating
const CACHE_NAME = CACHE_VERSION;

// CACHE FIX: Add timestamp for better cache invalidation
const CACHE_TIMESTAMP = Date.now();

// Resources that should always be cached
const STATIC_CACHE_URLS = [
    '/manifest.json'
];

// Resources that should be network-first (always check for updates)
const NETWORK_FIRST_URLS = [
	'/',
    '/index.html',
    '/styles/main.css',
    '/styles/components.css',
    '/styles/dimensions.css',
    '/styles/responsive.css',
    '/scripts/main.js',
    '/scripts/pwa.js',
    '/scripts/navigation.js',
    '/scripts/effects.js',
    '/scripts/utils.js'
];

// Install Service Worker
self.addEventListener('install', event => {
    console.log(`[Service Worker] Installing ${CACHE_VERSION}`);
    
    // Force the waiting service worker to become the active service worker
    self.skipWaiting();
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('[Service Worker] Caching static assets');
                // Only cache static resources during install
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .catch(error => {
                console.error('[Service Worker] Cache failed:', error);
            })
    );
});

// Activate Service Worker
self.addEventListener('activate', event => {
    console.log(`[Service Worker] Activating ${CACHE_VERSION}`);
    
    event.waitUntil(
        // Clean up old caches
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('[Service Worker] Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            // Take control of all clients immediately
            return self.clients.claim();
        })
    );
});

// Fetch event - Network first strategy for dynamic content
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') return;
    
    // Skip cross-origin requests
    if (url.origin !== location.origin) return;
    
    // Check if this is a network-first resource
    const isNetworkFirst = NETWORK_FIRST_URLS.some(networkUrl => 
        url.pathname === networkUrl || url.pathname.endsWith(networkUrl)
    );
    
    if (isNetworkFirst) {
        // Network first strategy - always try to get fresh content
        event.respondWith(
            fetch(request)
                .then(response => {
                    // Only cache successful responses
                    if (response && response.status === 200 && response.type === 'basic') {
                        // Clone the response before caching
                        const responseToCache = response.clone();

                        // Update cache with fresh content
                        caches.open(CACHE_NAME).then(cache => {
                            cache.put(request, responseToCache);
                        });
                    }

                    return response;
                })
                .catch(() => {
                    // If network fails, try cache
                    return caches.match(request).then(response => {
                        if (response) {
                            console.log('[Service Worker] Serving from cache (offline):', request.url);
                            return response;
                        }
                        // If not in cache, return offline page
                        return caches.match('/');
                    });
                })
        );
    } else {
        // Cache first strategy for other resources
        event.respondWith(
            caches.match(request).then(response => {
                if (response) {
                    // Return cached version
                    return response;
                }
                
                // Not in cache, fetch from network
                return fetch(request).then(response => {
                    // Don't cache non-successful responses
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }
                    
                    // Clone and cache the response
                    const responseToCache = response.clone();
                    caches.open(CACHE_NAME).then(cache => {
                        cache.put(request, responseToCache);
                    });
                    
                    return response;
                });
            })
        );
    }
});

// Listen for messages from the client
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        console.log('[Service Worker] Skip waiting received');
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CLEAR_CACHE') {
        console.log('[Service Worker] Clear cache received');
        event.waitUntil(
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        console.log('[Service Worker] Clearing cache:', cacheName);
                        return caches.delete(cacheName);
                    })
                );
            })
        );
    }
});

// Background sync for server status
self.addEventListener('sync', event => {
    if (event.tag === 'sync-servers') {
        event.waitUntil(syncServerStatus());
    }
});

// Helper function to sync server status
async function syncServerStatus() {
    try {
        // This would be replaced with actual server status checks
        console.log('[Service Worker] Syncing server status');
        return true;
    } catch (error) {
        console.error('[Service Worker] Sync failed:', error);
        return false;
    }
}

// Push notifications
self.addEventListener('push', event => {
    const options = {
        body: event.data ? event.data.text() : 'New update from xDREAM Gaming!',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        }
    };

    event.waitUntil(
        self.registration.showNotification('xDREAM Gaming', options)
    );
});

// Notification click
self.addEventListener('notificationclick', event => {
    event.notification.close();

    event.waitUntil(
        clients.openWindow('/')
    );
});