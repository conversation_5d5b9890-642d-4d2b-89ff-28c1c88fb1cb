/* Site Builder Specific Styles */

/* Override body styles for site builder */
.site-builder-body {
    overflow: auto !important;
    position: static !important;
    height: auto !important;
    min-height: 100vh;
}

/* Site Builder Container */
.site-builder-container {
    position: relative;
    z-index: 10;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.site-builder-header {
    background: rgba(10, 10, 10, 0.95);
    border-bottom: 2px solid var(--primary-glow, #00ffff);
    padding: var(--spacing-md, 1.5rem);
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md, 1.5rem);
}

.site-builder-title {
    font-size: var(--font-size-2xl, 3rem);
    color: var(--primary-glow, #00ffff);
    text-shadow: 0 0 20px var(--primary-glow, #00ffff);
    margin: 0;
}

.site-builder-subtitle {
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
    margin: 0;
    font-size: var(--font-size-md, 1.2rem);
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm, 1rem);
    flex-wrap: wrap;
}

/* Main Layout */
.site-builder-main {
    flex: 1;
    display: grid;
    grid-template-columns: 300px 1fr;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
    min-height: calc(100vh - 120px);
}

/* Sidebar */
.site-builder-sidebar {
    background: rgba(5, 5, 5, 0.9);
    border-right: 1px solid var(--primary-glow, #00ffff);
    padding: var(--spacing-md, 1.5rem);
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

.sidebar-section {
    margin-bottom: var(--spacing-xl, 3rem);
}

.sidebar-title {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-shadow: 0 0 10px var(--primary-glow, #00ffff);
}

.pages-list {
    margin-bottom: var(--spacing-md, 1.5rem);
}

.page-item {
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid var(--primary-glow, #00ffff);
    border-radius: 8px;
    padding: var(--spacing-sm, 1rem);
    margin-bottom: var(--spacing-sm, 1rem);
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 1rem);
}

.page-item:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
    transform: translateX(5px);
}

.page-item.active {
    background: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
}

.page-icon {
    font-size: var(--font-size-lg, 1.5rem);
    min-width: 30px;
}

.page-info {
    flex: 1;
}

.page-name {
    font-weight: bold;
    color: var(--text-primary, #ffffff);
    margin-bottom: 2px;
}

.page-subtitle {
    font-size: var(--font-size-xs, 0.875rem);
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
}

.add-page-btn {
    width: 100%;
    margin-bottom: var(--spacing-md, 1.5rem);
}

.templates-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 1rem);
}

.template-btn {
    background: rgba(255, 0, 255, 0.1);
    border: 1px solid var(--secondary-glow, #ff00ff);
    color: var(--text-primary, #ffffff);
    padding: var(--spacing-sm, 1rem);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    font-family: var(--font-primary, 'Courier New', monospace);
    font-size: var(--font-size-sm, 1rem);
}

.template-btn:hover {
    background: rgba(255, 0, 255, 0.2);
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
}

/* Editor Area */
.site-builder-editor {
    background: rgba(10, 10, 10, 0.8);
    padding: var(--spacing-md, 1.5rem);
    overflow-y: auto;
    max-height: calc(100vh - 120px);
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg, 2rem);
    padding-bottom: var(--spacing-md, 1.5rem);
    border-bottom: 1px solid var(--primary-glow, #00ffff);
    flex-wrap: wrap;
    gap: var(--spacing-md, 1.5rem);
}

.editor-title {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-xl, 2rem);
    margin: 0;
}

.editor-actions {
    display: flex;
    gap: var(--spacing-sm, 1rem);
    flex-wrap: wrap;
}

.editor-content {
    min-height: 400px;
}

.editor-placeholder {
    text-align: center;
    padding: var(--spacing-xl, 3rem);
    color: var(--text-secondary, rgba(255, 255, 255, 0.8));
}

.placeholder-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-md, 1.5rem);
}

/* Form Styles */
.form-container {
    max-width: 800px;
}

.form-section {
    background: rgba(0, 255, 255, 0.05);
    border: 1px solid var(--primary-glow, #00ffff);
    border-radius: 12px;
    padding: var(--spacing-lg, 2rem);
    margin-bottom: var(--spacing-lg, 2rem);
}

.form-section-title {
    color: var(--primary-glow, #00ffff);
    font-size: var(--font-size-lg, 1.5rem);
    margin-bottom: var(--spacing-md, 1.5rem);
    text-shadow: 0 0 10px var(--primary-glow, #00ffff);
}

.form-group {
    margin-bottom: var(--spacing-md, 1.5rem);
}

.form-group label {
    display: block;
    color: var(--text-primary, #ffffff);
    margin-bottom: var(--spacing-xs, 0.5rem);
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--primary-glow, #00ffff);
    border-radius: 6px;
    padding: var(--spacing-sm, 1rem);
    color: var(--text-primary, #ffffff);
    font-family: var(--font-primary, 'Courier New', monospace);
    font-size: var(--font-size-sm, 1rem);
    transition: all var(--transition-fast, 0.3s ease);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--secondary-glow, #ff00ff);
    box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md, 1.5rem);
}

/* Content Editor */
.content-editor {
    border: 1px solid var(--primary-glow, #00ffff);
    border-radius: 8px;
    overflow: hidden;
}

.content-editor-toolbar {
    background: rgba(0, 255, 255, 0.1);
    padding: var(--spacing-sm, 1rem);
    border-bottom: 1px solid var(--primary-glow, #00ffff);
    display: flex;
    gap: var(--spacing-sm, 1rem);
    flex-wrap: wrap;
}

.toolbar-btn {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid var(--primary-glow, #00ffff);
    color: var(--text-primary, #ffffff);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    font-family: var(--font-primary, 'Courier New', monospace);
}

.toolbar-btn:hover {
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.content-editor-area {
    background: rgba(0, 0, 0, 0.7);
    color: var(--text-primary, #ffffff);
    border: none;
    padding: var(--spacing-md, 1.5rem);
    font-family: var(--font-primary, 'Courier New', monospace);
    font-size: var(--font-size-sm, 1rem);
    min-height: 300px;
    width: 100%;
    resize: vertical;
}

.content-editor-area:focus {
    outline: none;
}

/* Preview Panel */
.site-builder-preview {
    position: fixed;
    top: 0;
    right: -50%;
    width: 50%;
    height: 100vh;
    background: rgba(5, 5, 5, 0.95);
    border-left: 2px solid var(--primary-glow, #00ffff);
    z-index: 200;
    transition: right var(--transition-medium, 0.5s ease);
    display: flex;
    flex-direction: column;
}

.site-builder-preview.active {
    right: 0;
}

.preview-header {
    background: rgba(0, 255, 255, 0.1);
    padding: var(--spacing-md, 1.5rem);
    border-bottom: 1px solid var(--primary-glow, #00ffff);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.preview-title {
    color: var(--primary-glow, #00ffff);
    margin: 0;
    font-size: var(--font-size-lg, 1.5rem);
}

.close-preview-btn {
    background: rgba(255, 0, 0, 0.2);
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    font-family: var(--font-primary, 'Courier New', monospace);
}

.close-preview-btn:hover {
    background: rgba(255, 0, 0, 0.3);
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.preview-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md, 1.5rem);
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: rgba(10, 10, 10, 0.95);
    border: 2px solid var(--primary-glow, #00ffff);
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
}

.modal-header {
    background: rgba(0, 255, 255, 0.1);
    padding: var(--spacing-md, 1.5rem);
    border-bottom: 1px solid var(--primary-glow, #00ffff);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    color: var(--primary-glow, #00ffff);
    margin: 0;
    font-size: var(--font-size-lg, 1.5rem);
}

.modal-close {
    background: rgba(255, 0, 0, 0.2);
    border: 1px solid #ff0000;
    color: #ff0000;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast, 0.3s ease);
    font-family: var(--font-primary, 'Courier New', monospace);
}

.modal-close:hover {
    background: rgba(255, 0, 0, 0.3);
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.modal-body {
    padding: var(--spacing-lg, 2rem);
}

.modal-footer {
    padding: var(--spacing-md, 1.5rem);
    border-top: 1px solid var(--primary-glow, #00ffff);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm, 1rem);
}

/* Button Variants */
.quantum-button.secondary {
    background: rgba(128, 128, 128, 0.2);
    border-color: #888;
    color: #ccc;
}

.quantum-button.secondary:hover {
    background: rgba(128, 128, 128, 0.3);
    box-shadow: 0 0 15px rgba(128, 128, 128, 0.3);
}

.quantum-button.danger {
    background: rgba(255, 0, 0, 0.2);
    border-color: #ff0000;
    color: #ff0000;
}

.quantum-button.danger:hover {
    background: rgba(255, 0, 0, 0.3);
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.3);
}

.quantum-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Message Container */
.message-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 2000;
    max-width: 400px;
}

.message {
    background: rgba(10, 10, 10, 0.95);
    border: 1px solid var(--primary-glow, #00ffff);
    border-radius: 8px;
    padding: var(--spacing-md, 1.5rem);
    margin-bottom: var(--spacing-sm, 1rem);
    color: var(--text-primary, #ffffff);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
    animation: slideInRight 0.3s ease;
}

.message.success {
    border-color: #00ff00;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

.message.error {
    border-color: #ff0000;
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.3);
}

.message.warning {
    border-color: #ffff00;
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.3);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .site-builder-main {
        grid-template-columns: 250px 1fr;
    }
}

@media (max-width: 768px) {
    .site-builder-main {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .site-builder-sidebar {
        max-height: 300px;
        border-right: none;
        border-bottom: 1px solid var(--primary-glow, #00ffff);
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .site-builder-preview {
        width: 100%;
        right: -100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

@media (max-width: 480px) {
    .site-builder-header {
        padding: var(--spacing-sm, 1rem);
    }
    
    .site-builder-title {
        font-size: var(--font-size-xl, 2rem);
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .editor-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .editor-actions {
        justify-content: center;
    }
}

/* Dark scrollbars */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-glow, #00ffff);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-glow, #ff00ff);
}