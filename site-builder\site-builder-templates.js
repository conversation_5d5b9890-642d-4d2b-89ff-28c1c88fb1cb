// Site Builder Templates - Template Management System
class SiteBuilderTemplates {
    constructor() {
        this.templates = {};
        this.init();
    }

    async init() {
        this.loadTemplates();
        this.setupTemplateEventListeners();
        console.log('📋 Site Builder Templates initialized');
    }

    loadTemplates() {
        this.templates = {
            'game-server': {
                name: 'Game Server',
                icon: '🎮',
                description: 'Perfect for game server pages with server information, player counts, and connection details',
                category: 'Gaming',
                defaultContent: [
                    {
                        type: 'text',
                        content: `
                            <div class="dimension-header">
                                <h2 class="dimension-title">GAME SERVER</h2>
                                <div class="dimension-subtitle">Server Information</div>
                            </div>
                        `
                    },
                    {
                        type: 'server-node',
                        serverName: 'xDREAM Game Server',
                        serverAddress: 'server.xdreamserver.com:27015',
                        serverInfo: 'ACTIVE • MODDED • PVP ENABLED'
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="server-settings-container">
                                <h3 class="settings-title">⚙️ Server Settings</h3>
                                <div class="settings-grid">
                                    <div class="settings-panel">
                                        <h4>🎯 Game Configuration</h4>
                                        <div class="settings-list">
                                            <div class="setting-item">🎮 <strong>Game Mode:</strong> PvP</div>
                                            <div class="setting-item">👥 <strong>Max Players:</strong> 32</div>
                                            <div class="setting-item">🔧 <strong>Mods:</strong> Enabled</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                    }
                ],
                backgroundEffects: {
                    particles: true,
                    matrix: false,
                    glitch: false
                },
                accentColor: '#ff6b9d'
            },
            'community': {
                name: 'Community',
                icon: '💬',
                description: 'Ideal for community pages, Discord invites, and social interaction hubs',
                category: 'Social',
                defaultContent: [
                    {
                        type: 'text',
                        content: `
                            <div class="dimension-header">
                                <h2 class="dimension-title">COMMUNITY HUB</h2>
                                <div class="dimension-subtitle">Join Our Gaming Community</div>
                            </div>
                        `
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="community-section">
                                <h3 class="section-title">🎮 Connect With Gamers</h3>
                                <div class="community-card">
                                    <div class="community-content">
                                        <p>Join thousands of players in our active gaming community. Share strategies, find teammates, and stay updated with the latest server news.</p>
                                    </div>
                                </div>
                            </div>
                        `
                    },
                    {
                        type: 'button',
                        text: 'JOIN DISCORD',
                        url: 'https://discord.xdreamserver.com',
                        style: 'quantum-button'
                    },
                    {
                        type: 'button',
                        text: 'VISIT FORUMS',
                        url: '#',
                        style: 'quantum-button secondary'
                    }
                ],
                backgroundEffects: {
                    particles: false,
                    matrix: true,
                    glitch: false
                },
                accentColor: '#00d4ff'
            },
            'information': {
                name: 'Information',
                icon: '📋',
                description: 'Great for guides, tutorials, rules, and informational content',
                category: 'Content',
                defaultContent: [
                    {
                        type: 'text',
                        content: `
                            <div class="dimension-header">
                                <h2 class="dimension-title">INFORMATION CENTER</h2>
                                <div class="dimension-subtitle">Important Information</div>
                            </div>
                        `
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="info-section">
                                <h3 class="section-title">📖 Getting Started</h3>
                                <div class="info-card">
                                    <div class="info-content">
                                        <p>Welcome to our gaming community! Here you'll find all the essential information you need to get started.</p>
                                        <ul>
                                            <li>📋 Read the server rules</li>
                                            <li>🔧 Install required mods</li>
                                            <li>💬 Join our Discord</li>
                                            <li>🎮 Start playing!</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        `
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="info-section">
                                <h3 class="section-title">⚠️ Important Rules</h3>
                                <div class="info-card">
                                    <div class="info-content">
                                        <p>Please follow these guidelines to ensure everyone has a great experience:</p>
                                        <ol>
                                            <li>Respect all players</li>
                                            <li>No cheating or exploiting</li>
                                            <li>Keep chat family-friendly</li>
                                            <li>Report issues to staff</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        `
                    }
                ],
                backgroundEffects: {
                    particles: false,
                    matrix: false,
                    glitch: true
                },
                accentColor: '#ffaa00'
            },
            'custom': {
                name: 'Custom',
                icon: '🛠️',
                description: 'Blank template for complete customization and unique content',
                category: 'Custom',
                defaultContent: [
                    {
                        type: 'text',
                        content: `
                            <div class="dimension-header">
                                <h2 class="dimension-title">CUSTOM PAGE</h2>
                                <div class="dimension-subtitle">Build Your Vision</div>
                            </div>
                        `
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="custom-section">
                                <h3 class="section-title">🎨 Your Content Here</h3>
                                <div class="custom-card">
                                    <div class="custom-content">
                                        <p>This is a blank template ready for your creativity. Add any content blocks you need to create the perfect page for your community.</p>
                                    </div>
                                </div>
                            </div>
                        `
                    }
                ],
                backgroundEffects: {
                    particles: false,
                    matrix: true,
                    glitch: false
                },
                accentColor: '#00ffff'
            },
            'event': {
                name: 'Event',
                icon: '🎉',
                description: 'Perfect for announcing events, tournaments, and special occasions',
                category: 'Events',
                defaultContent: [
                    {
                        type: 'text',
                        content: `
                            <div class="dimension-header">
                                <h2 class="dimension-title">SPECIAL EVENT</h2>
                                <div class="dimension-subtitle">Join the Action</div>
                            </div>
                        `
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="event-section">
                                <h3 class="section-title">🏆 Tournament Details</h3>
                                <div class="event-card">
                                    <div class="event-content">
                                        <div class="event-info">
                                            <div class="event-item">📅 <strong>Date:</strong> Coming Soon</div>
                                            <div class="event-item">⏰ <strong>Time:</strong> TBD</div>
                                            <div class="event-item">🎮 <strong>Game:</strong> Your Game Here</div>
                                            <div class="event-item">🏆 <strong>Prize:</strong> Amazing Rewards</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                    },
                    {
                        type: 'button',
                        text: 'REGISTER NOW',
                        url: '#',
                        style: 'quantum-button'
                    },
                    {
                        type: 'button',
                        text: 'VIEW RULES',
                        url: '#',
                        style: 'quantum-button secondary'
                    }
                ],
                backgroundEffects: {
                    particles: true,
                    matrix: false,
                    glitch: true
                },
                accentColor: '#ff6b35'
            },
            'store': {
                name: 'Store',
                icon: '🛒',
                description: 'Showcase products, donations, and in-game purchases',
                category: 'Commerce',
                defaultContent: [
                    {
                        type: 'text',
                        content: `
                            <div class="dimension-header">
                                <h2 class="dimension-title">GAME STORE</h2>
                                <div class="dimension-subtitle">Support Our Community</div>
                            </div>
                        `
                    },
                    {
                        type: 'text',
                        content: `
                            <div class="store-section">
                                <h3 class="section-title">💎 Premium Packages</h3>
                                <div class="store-grid">
                                    <div class="store-item">
                                        <div class="item-header">🥉 Bronze Package</div>
                                        <div class="item-price">$5.00</div>
                                        <div class="item-features">
                                            <div class="feature">✨ Special Badge</div>
                                            <div class="feature">🎁 Starter Kit</div>
                                            <div class="feature">💬 VIP Chat</div>
                                        </div>
                                    </div>
                                    <div class="store-item">
                                        <div class="item-header">🥈 Silver Package</div>
                                        <div class="item-price">$10.00</div>
                                        <div class="item-features">
                                            <div class="feature">✨ Premium Badge</div>
                                            <div class="feature">🎁 Advanced Kit</div>
                                            <div class="feature">💬 VIP Chat</div>
                                            <div class="feature">🚀 Fast Travel</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                    },
                    {
                        type: 'button',
                        text: 'VISIT STORE',
                        url: 'https://store.xdreamserver.com',
                        style: 'quantum-button'
                    }
                ],
                backgroundEffects: {
                    particles: true,
                    matrix: false,
                    glitch: false
                },
                accentColor: '#ff00ff'
            }
        };

        console.log(`📋 Loaded ${Object.keys(this.templates).length} templates`);
    }

    getTemplate(templateName) {
        return this.templates[templateName] || null;
    }

    getAllTemplates() {
        return this.templates;
    }

    getTemplatesByCategory(category) {
        return Object.entries(this.templates)
            .filter(([_, template]) => template.category === category)
            .reduce((acc, [key, template]) => {
                acc[key] = template;
                return acc;
            }, {});
    }

    applyTemplate(pageData, templateName) {
        const template = this.getTemplate(templateName);
        if (!template) {
            console.warn(`⚠️ Template not found: ${templateName}`);
            return pageData;
        }

        return {
            ...pageData,
            content: [...template.defaultContent],
            backgroundEffects: { ...template.backgroundEffects },
            accentColor: pageData.accentColor || template.accentColor
        };
    }

    createTemplatePreview(templateName) {
        const template = this.getTemplate(templateName);
        if (!template) return '';

        return `
            <div class="template-preview" data-template="${templateName}">
                <div class="template-header">
                    <div class="template-icon">${template.icon}</div>
                    <div class="template-info">
                        <div class="template-name">${template.name}</div>
                        <div class="template-category">${template.category}</div>
                    </div>
                </div>
                <div class="template-description">
                    ${template.description}
                </div>
                <div class="template-features">
                    <div class="feature-item">📄 ${template.defaultContent.length} Content Blocks</div>
                    <div class="feature-item">🎨 ${template.accentColor} Theme</div>
                    <div class="feature-item">✨ ${Object.values(template.backgroundEffects).filter(Boolean).length} Effects</div>
                </div>
            </div>
        `;
    }

    renderTemplateSelector() {
        const categories = [...new Set(Object.values(this.templates).map(t => t.category))];
        
        let html = '<div class="template-selector">';
        
        categories.forEach(category => {
            const categoryTemplates = this.getTemplatesByCategory(category);
            
            html += `
                <div class="template-category-section">
                    <h4 class="category-title">${category}</h4>
                    <div class="templates-grid">
            `;
            
            Object.entries(categoryTemplates).forEach(([key, template]) => {
                html += `
                    <div class="template-card" data-template="${key}">
                        <div class="template-card-header">
                            <span class="template-card-icon">${template.icon}</span>
                            <span class="template-card-name">${template.name}</span>
                        </div>
                        <div class="template-card-description">
                            ${template.description}
                        </div>
                        <button class="template-select-btn" onclick="siteBuilderTemplates.selectTemplate('${key}')">
                            Use Template
                        </button>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    selectTemplate(templateName) {
        const template = this.getTemplate(templateName);
        if (!template) return;

        // Fill the add page form with template data
        const pageTemplateSelect = document.getElementById('pageTemplate');
        if (pageTemplateSelect) {
            pageTemplateSelect.value = templateName;
        }

        const accentColorInput = document.getElementById('accentColor');
        if (accentColorInput) {
            accentColorInput.value = template.accentColor;
        }

        console.log(`📋 Selected template: ${template.name}`);
        window.siteBuilderCore.showMessage(`Template "${template.name}" selected`, 'success');
    }

    exportTemplate(templateName) {
        const template = this.getTemplate(templateName);
        if (!template) return;

        const exportData = {
            name: template.name,
            template: template,
            exported: new Date().toISOString(),
            version: '1.0.0'
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `xdream-template-${templateName}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log(`📤 Exported template: ${template.name}`);
        window.siteBuilderCore.showMessage(`Template "${template.name}" exported`, 'success');
    }

    async importTemplate(file) {
        try {
            const text = await file.text();
            const data = JSON.parse(text);

            if (data.template && data.name) {
                const templateKey = data.name.toLowerCase().replace(/\s+/g, '-');
                this.templates[templateKey] = data.template;
                
                console.log(`📥 Imported template: ${data.name}`);
                window.siteBuilderCore.showMessage(`Template "${data.name}" imported successfully`, 'success');
                return true;
            } else {
                throw new Error('Invalid template format');
            }
        } catch (error) {
            console.error('❌ Error importing template:', error);
            window.siteBuilderCore.showMessage('Error importing template - invalid file format', 'error');
            return false;
        }
    }

    setupTemplateEventListeners() {
        // Template buttons in sidebar
        const templateBtns = document.querySelectorAll('.template-btn');
        templateBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const templateName = btn.dataset.template;
                if (templateName) {
                    this.selectTemplate(templateName);
                    // Show add page modal with template pre-selected
                    window.siteBuilderUI.showAddPageModal();
                }
            });
        });

        console.log('🎧 Template event listeners setup complete');
    }

    // Content Block Templates
    getContentBlockTemplates() {
        return {
            'server-info-advanced': {
                name: 'Advanced Server Info',
                type: 'text',
                content: `
                    <div class="server-info-advanced">
                        <h3 class="info-title">🖥️ Server Information</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Status:</span>
                                <span class="info-value online">🟢 Online</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Players:</span>
                                <span class="info-value">24/32</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Uptime:</span>
                                <span class="info-value">99.9%</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Version:</span>
                                <span class="info-value">Latest</span>
                            </div>
                        </div>
                    </div>
                `
            },
            'feature-list': {
                name: 'Feature List',
                type: 'text',
                content: `
                    <div class="feature-list">
                        <h3 class="feature-title">✨ Server Features</h3>
                        <div class="features-grid">
                            <div class="feature-item">🎮 Custom Gameplay</div>
                            <div class="feature-item">🛡️ Anti-Cheat Protection</div>
                            <div class="feature-item">👥 Active Community</div>
                            <div class="feature-item">🔧 Regular Updates</div>
                            <div class="feature-item">💬 24/7 Support</div>
                            <div class="feature-item">🏆 Events & Tournaments</div>
                        </div>
                    </div>
                `
            },
            'stats-display': {
                name: 'Statistics Display',
                type: 'text',
                content: `
                    <div class="stats-display">
                        <h3 class="stats-title">📊 Server Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">1,234</div>
                                <div class="stat-label">Total Players</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">567</div>
                                <div class="stat-label">Active Today</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">89</div>
                                <div class="stat-label">Online Now</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">99.9%</div>
                                <div class="stat-label">Uptime</div>
                            </div>
                        </div>
                    </div>
                `
            }
        };
    }
}

// Global instance
window.siteBuilderTemplates = new SiteBuilderTemplates();