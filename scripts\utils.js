// scripts/utils.js - Utility functions with security fixes

// Copy to clipboard function with better error handling
function copyToClipboard(text) {
    // SECURITY FIX: Validate input
    if (typeof text !== 'string' || !text) {
        console.error('Invalid text provided to copyToClipboard');
        return;
    }
    
    // Modern clipboard API with fallback
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopyNotification(text);
        }).catch((err) => {
            console.error('Clipboard write failed:', err);
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

// Fallback for older browsers or non-secure contexts
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.cssText = 'position: fixed; top: -9999px; left: -9999px;';
    textArea.setAttribute('readonly', ''); // Prevent keyboard from showing on mobile
    
    document.body.appendChild(textArea);
    
    // Select text
    if (navigator.userAgent.match(/ipad|iphone/i)) {
        // iOS specific selection
        const range = document.createRange();
        range.selectNodeContents(textArea);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
        textArea.setSelectionRange(0, 999999);
    } else {
        textArea.select();
    }
    
    try {
        document.execCommand('copy');
        showCopyNotification(text);
    } catch (err) {
        console.error('Fallback copy failed:', err);
        // Show manual copy instruction
        alert(`Please copy manually: ${text}`);
    } finally {
        document.body.removeChild(textArea);
    }
}

// Show copy notification with animation
function showCopyNotification(text) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'copy-notification';
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.5);
        background: linear-gradient(45deg, #00ffff, #ff00ff);
        color: #000;
        padding: 1rem 2rem;
        border-radius: 10px;
        font-weight: bold;
        z-index: 10000;
        animation: copyNotification 2s ease-in-out forwards;
        font-size: 1rem;
        box-shadow: 0 4px 20px rgba(0, 255, 255, 0.4);
        pointer-events: none;
        white-space: nowrap;
        max-width: 90vw;
        overflow: hidden;
        text-overflow: ellipsis;
    `;
    
    // Truncate long text for notification
    const displayText = text.length > 30 ? text.substring(0, 27) + '...' : text;
    notification.textContent = `✓ ${displayText} copied!`;
    document.body.appendChild(notification);

    // Add animation keyframe if not exists
    if (!document.querySelector('#copy-notification-styles')) {
        const style = document.createElement('style');
        style.id = 'copy-notification-styles';
        style.textContent = `
            @keyframes copyNotification {
                0% { 
                    opacity: 0; 
                    transform: translate(-50%, -50%) scale(0.5); 
                }
                20% { 
                    opacity: 1; 
                    transform: translate(-50%, -50%) scale(1.1); 
                }
                80% { 
                    opacity: 1; 
                    transform: translate(-50%, -50%) scale(1); 
                }
                100% { 
                    opacity: 0; 
                    transform: translate(-50%, -50%) scale(0.8); 
                }
            }
        `;
        document.head.appendChild(style);
    }

    // Clean up notification
    setTimeout(() => {
        if (notification.parentNode) {
            document.body.removeChild(notification);
        }
    }, 2000);

    // Haptic feedback on mobile
    if ('vibrate' in navigator) {
        navigator.vibrate(50);
    }
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Throttle function
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Check if device is mobile
function isMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// Check if device has touch
function hasTouch() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// Format server status
function formatServerStatus(status) {
    const statusMap = {
        'online': { text: 'Online', color: '#00ff00' },
        'offline': { text: 'Offline', color: '#ff0000' },
        'maintenance': { text: 'Maintenance', color: '#ffff00' }
    };
    
    const statusInfo = statusMap[status] || statusMap['offline'];
    return `<span style="color: ${statusInfo.color}">● ${statusInfo.text}</span>`;
}

// Create loading spinner
function createLoadingSpinner(parent) {
    const spinner = document.createElement('div');
    spinner.className = 'loading-spinner';
    spinner.setAttribute('role', 'status');
    spinner.setAttribute('aria-label', 'Loading');
    parent.appendChild(spinner);
    return spinner;
}

// Remove loading spinner
function removeLoadingSpinner(spinner) {
    if (spinner && spinner.parentNode) {
        spinner.parentNode.removeChild(spinner);
    }
}

// Smooth scroll to element
function smoothScrollTo(element, duration = 1000) {
    if (!element) return;
    
    const start = window.pageYOffset;
    const target = element.getBoundingClientRect().top + start;
    const distance = target - start;
    const startTime = performance.now();

    function animation(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeInOutCubic = progress < 0.5
            ? 4 * progress * progress * progress
            : 1 - Math.pow(-2 * progress + 2, 3) / 2;
        
        window.scrollTo(0, start + distance * easeInOutCubic);

        if (elapsed < duration) {
            requestAnimationFrame(animation);
        }
    }

    requestAnimationFrame(animation);
}

// Get random element from array
function getRandomElement(array) {
    if (!Array.isArray(array) || array.length === 0) return null;
    return array[Math.floor(Math.random() * array.length)];
}

// SECURITY FIX: Toggle Passives Display with proper event handling
function togglePassives(event) {
    // Prevent event bubbling
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }
    
    const passivesContainer = document.getElementById('allPassives');
    const button = event ? event.target : document.querySelector('[aria-controls="allPassives"]');
    
    if (!passivesContainer || !button) {
        console.error('Required elements not found for togglePassives');
        return;
    }
    
    const isHidden = passivesContainer.style.display === 'none';
    
    if (isHidden) {
        passivesContainer.style.display = 'block';
        button.textContent = 'Hide All Passives';
        button.setAttribute('aria-expanded', 'true');
        passivesContainer.setAttribute('aria-hidden', 'false');
        
        // Smooth scroll to the passives section
        setTimeout(() => {
            passivesContainer.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'nearest',
                inline: 'nearest' 
            });
        }, 100);
    } else {
        passivesContainer.style.display = 'none';
        button.textContent = 'Show All Passives';
        button.setAttribute('aria-expanded', 'false');
        passivesContainer.setAttribute('aria-hidden', 'true');
    }
}

// Dune server configuration variables (replace with actual values)
const duneServerConfig = {
    ServerName: 'xDREAM PvEvP',
    MaxPlayers: '40',
    Region: 'North America East'
};

// Function to replace server variables in text content
function replaceDuneVariables() {
    // Find all elements with server variables
    const elements = document.querySelectorAll('.server-var');
    
    elements.forEach(element => {
        let text = element.textContent;
        
        // Replace each variable
        Object.keys(duneServerConfig).forEach(key => {
            const placeholder = `<${key}>`;
            if (text.includes(placeholder)) {
                text = text.replace(placeholder, duneServerConfig[key]);
            }
        });
        
        element.textContent = text;
    });
}

// Update server status (mock function - replace with actual server query)
function updateDuneServerStatus() {
    const statusIndicator = document.querySelector('.dune-dimension .status-indicator');
    const playerCount = document.querySelector('.dune-dimension .player-text');
    
    if (statusIndicator && playerCount) {
        // Mock data - replace with actual server status API call
        const isOnline = Math.random() > 0.1; // 90% chance online
        const currentPlayers = isOnline ? Math.floor(Math.random() * parseInt(duneServerConfig.MaxPlayers)) : 0;
        
        if (isOnline) {
            statusIndicator.classList.remove('offline');
            statusIndicator.classList.add('online');
            statusIndicator.querySelector('.status-text').textContent = 'Server Online';
            playerCount.textContent = `${currentPlayers}/${duneServerConfig.MaxPlayers} Players`;
        } else {
            statusIndicator.classList.remove('online');
            statusIndicator.classList.add('offline');
            statusIndicator.querySelector('.status-text').textContent = 'Server Offline';
            playerCount.textContent = `0/${duneServerConfig.MaxPlayers} Players`;
        }
    }
}

// PERFORMANCE FIX: Initialize utilities with proper timing
function initializeUtilities() {
    // Replace Dune variables when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', replaceDuneVariables);
    } else {
        replaceDuneVariables();
    }
    
    // Update server status periodically
    updateDuneServerStatus();
    setInterval(updateDuneServerStatus, 30000);
}

// Initialize when script loads
initializeUtilities();

// Export functions for use in other modules
window.xDreamUtils = {
    copyToClipboard,
    debounce,
    throttle,
    isMobile,
    hasTouch,
    formatServerStatus,
    createLoadingSpinner,
    removeLoadingSpinner,
    smoothScrollTo,
    getRandomElement,
    togglePassives
};